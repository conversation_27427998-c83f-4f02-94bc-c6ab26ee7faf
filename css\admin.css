/* Admin Panel Styles */
.admin-layout {
    min-height: 100vh;
    background: #f8f9fa;
    padding-top: 80px;
}

.admin-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    gap: 20px;
    padding: 20px;
}

/* Sidebar Styles */
.admin-sidebar {
    width: 280px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 24px;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.admin-nav-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.admin-nav-title i {
    color: #3182ce;
    font-size: 1.5rem;
}

.admin-nav-menu {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.admin-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border: none;
    background: transparent;
    border-radius: 8px;
    color: #4a5568;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
}

.admin-nav-item:hover {
    background: #f7fafc;
    color: #2d3748;
}

.admin-nav-item.active {
    background: #3182ce;
    color: white;
}

.admin-nav-item i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Main Content Styles */
.admin-main {
    flex: 1;
    min-width: 0;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.admin-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.admin-header h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.admin-header p {
    color: #718096;
    margin: 0;
    font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: #3182ce; }
.stat-card:nth-child(2) .stat-icon { background: #38a169; }
.stat-card:nth-child(3) .stat-icon { background: #d69e2e; }
.stat-card:nth-child(4) .stat-icon { background: #e53e3e; }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 4px 0;
}

.stat-content p {
    color: #4a5568;
    margin: 0 0 8px 0;
    font-weight: 500;
}

.stat-change {
    font-size: 0.875rem;
    color: #718096;
}

/* Admin Grid */
.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.admin-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.card-content {
    padding: 24px;
}

/* Controls */
.admin-controls {
    background: white;
    border-radius: 12px;
    padding: 20px 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.search-filters {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input, .filter-select {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
    min-width: 200px;
}

.search-input:focus, .filter-select:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-success, .btn-danger {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #3182ce;
    color: white;
}

.btn-primary:hover {
    background: #2c5aa0;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-success {
    background: #38a169;
    color: white;
}

.btn-success:hover {
    background: #2f855a;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background: #c53030;
}

/* Tables */
.table-container {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.admin-table th {
    background: #f7fafc;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 1px solid #e2e8f0;
}

.admin-table td {
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.admin-table tr:hover {
    background: #f7fafc;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-details h4 {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
    color: #2d3748;
}

.user-details p {
    margin: 0;
    font-size: 0.75rem;
    color: #718096;
}

.product-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.product-image {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-active {
    background: #c6f6d5;
    color: #22543d;
}

.status-inactive {
    background: #fed7d7;
    color: #742a2a;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.role-admin {
    background: #bee3f8;
    color: #2a4365;
}

.role-moderator {
    background: #fbb6ce;
    color: #702459;
}

.role-user {
    background: #e2e8f0;
    color: #4a5568;
}

.verified-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.verified-true {
    background: #c6f6d5;
    color: #22543d;
}

.verified-false {
    background: #fed7d7;
    color: #742a2a;
}

.rating-stars {
    color: #d69e2e;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.edit {
    background: #bee3f8;
    color: #2a4365;
}

.action-btn.edit:hover {
    background: #90cdf4;
}

.action-btn.delete {
    background: #fed7d7;
    color: #742a2a;
}

.action-btn.delete:hover {
    background: #feb2b2;
}

.action-btn.verify {
    background: #c6f6d5;
    color: #22543d;
}

.action-btn.verify:hover {
    background: #9ae6b4;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
}

.pagination button:hover {
    background: #f7fafc;
}

.pagination button.active {
    background: #3182ce;
    color: white;
    border-color: #3182ce;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Activity Lists */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin: 0 0 4px 0;
    font-size: 0.875rem;
    font-weight: 500;
    color: #2d3748;
}

.activity-content p {
    margin: 0;
    font-size: 0.75rem;
    color: #718096;
}

.activity-time {
    font-size: 0.75rem;
    color: #a0aec0;
}

/* Loading States */
.loading {
    text-align: center;
    padding: 40px;
    color: #718096;
    font-style: italic;
}

/* Category List */
.category-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f7fafc;
    border-radius: 6px;
}

.category-name {
    font-weight: 500;
    color: #2d3748;
}

.category-count {
    background: #3182ce;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-container {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        position: static;
    }
    
    .admin-nav-menu {
        flex-direction: row;
        overflow-x: auto;
        gap: 4px;
    }
    
    .admin-nav-item {
        white-space: nowrap;
        min-width: fit-content;
    }
}

@media (max-width: 768px) {
    .admin-container {
        padding: 10px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-filters {
        justify-content: stretch;
    }
    
    .search-input, .filter-select {
        min-width: auto;
        flex: 1;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f7fafc;
    color: #4a5568;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #4a5568;
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.form-control[readonly] {
    background: #f7fafc;
    color: #718096;
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

/* Chart Containers */
canvas {
    max-width: 100%;
    height: auto;
}

/* Error States */
.error-message {
    background: #fed7d7;
    color: #742a2a;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 0.875rem;
}

.success-message {
    background: #c6f6d5;
    color: #22543d;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 0.875rem;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #cbd5e0;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 1.125rem;
    font-weight: 500;
    color: #4a5568;
}

.empty-state p {
    margin: 0;
    font-size: 0.875rem;
}

/* Tooltips */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #2d3748;
    color: white;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* Admin-only elements */
.admin-only {
    display: none;
}

body.admin .admin-only {
    display: block;
}

body.admin .profile-dropdown .admin-only {
    display: flex;
}

/* Scrollbar Styling */
.table-container::-webkit-scrollbar,
.activity-list::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.table-container::-webkit-scrollbar-track,
.activity-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb,
.activity-list::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover,
.activity-list::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Bulk Operations Styles */
.bulk-operation-form {
    max-width: 600px;
}

.bulk-operation-form .form-group {
    margin-bottom: 20px;
}

.bulk-operation-form textarea {
    min-height: 80px;
    resize: vertical;
}

/* Export Options */
.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.export-item {
    padding: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f7fafc;
}

.export-item h4 {
    margin: 0 0 8px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
}

.export-item p {
    margin: 0 0 16px 0;
    color: #718096;
    font-size: 0.875rem;
}

.export-buttons {
    display: flex;
    gap: 8px;
}

.export-buttons button {
    flex: 1;
}

/* System Health Styles */
.health-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.health-item {
    padding: 16px;
    background: #f7fafc;
    border-radius: 8px;
    border-left: 4px solid #3182ce;
}

.health-item.healthy {
    border-left-color: #38a169;
}

.health-item.warning {
    border-left-color: #d69e2e;
}

.health-item.error {
    border-left-color: #e53e3e;
}

.health-item h4 {
    margin: 0 0 8px 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #4a5568;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.health-item .value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
}

.health-item .unit {
    font-size: 0.875rem;
    color: #718096;
    margin-left: 4px;
}

/* System Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.setting-info h4 {
    margin: 0 0 4px 0;
    font-size: 1rem;
    font-weight: 500;
    color: #2d3748;
}

.setting-info p {
    margin: 0;
    font-size: 0.875rem;
    color: #718096;
}

.setting-value {
    font-weight: 600;
    color: #3182ce;
}

.setting-value.boolean.true {
    color: #38a169;
}

.setting-value.boolean.false {
    color: #e53e3e;
}

/* Log Type Badges */
.log-type-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.log-type-user_login {
    background: #bee3f8;
    color: #2a4365;
}

.log-type-admin_action {
    background: #fbb6ce;
    color: #702459;
}

.log-type-system_error {
    background: #fed7d7;
    color: #742a2a;
}

.log-type-default {
    background: #e2e8f0;
    color: #4a5568;
}

/* Enhanced Tables */
.admin-table .id-cell {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    color: #718096;
}

.admin-table .timestamp-cell {
    font-size: 0.875rem;
    color: #4a5568;
    white-space: nowrap;
}

/* Bulk Selection */
.bulk-select-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
}

.bulk-select-header input[type="checkbox"] {
    margin: 0;
}

.bulk-actions {
    display: none;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #bee3f8;
    border-bottom: 1px solid #90cdf4;
}

.bulk-actions.show {
    display: flex;
}

.bulk-actions .selected-count {
    font-weight: 500;
    color: #2a4365;
}

/* Progress Indicators */
.progress-indicator {
    display: none;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #f0fff4;
    border: 1px solid #9ae6b4;
    border-radius: 6px;
    margin-bottom: 16px;
}

.progress-indicator.show {
    display: flex;
}

.progress-indicator .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #c6f6d5;
    border-top: 2px solid #38a169;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .export-options {
        grid-template-columns: 1fr;
    }

    .health-grid {
        grid-template-columns: 1fr;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .bulk-operation-form {
        max-width: 100%;
    }

    .export-buttons {
        flex-direction: column;
    }

    .bulk-select-header {
        flex-wrap: wrap;
    }

    .bulk-actions {
        flex-wrap: wrap;
    }
}
