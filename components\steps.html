<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Spotless Hungry Crocodile</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta charset="utf-8" />
    <meta property="twitter:card" content="summary_large_image" />

    <style data-tag="reset-style-sheet">
      html {  line-height: 1.15;}body {  margin: 0;}* {  box-sizing: border-box;  border-width: 0;  border-style: solid;}p,li,ul,pre,div,h1,h2,h3,h4,h5,h6,figure,blockquote,figcaption {  margin: 0;  padding: 0;}button {  background-color: transparent;}button,input,optgroup,select,textarea {  font-family: inherit;  font-size: 100%;  line-height: 1.15;  margin: 0;}button,select {  text-transform: none;}button,[type="button"],[type="reset"],[type="submit"] {  -webkit-appearance: button;}button::-moz-focus-inner,[type="button"]::-moz-focus-inner,[type="reset"]::-moz-focus-inner,[type="submit"]::-moz-focus-inner {  border-style: none;  padding: 0;}button:-moz-focus,[type="button"]:-moz-focus,[type="reset"]:-moz-focus,[type="submit"]:-moz-focus {  outline: 1px dotted ButtonText;}a {  color: inherit;  text-decoration: inherit;}input {  padding: 2px 4px;}img {  display: block;}html { scroll-behavior: smooth  }
    </style>
    <style data-tag="default-style-sheet">
      html {
        font-family: Inter;
        font-size: 16px;
      }

      body {
        font-weight: 400;
        font-style:normal;
        text-decoration: none;
        text-transform: none;
        letter-spacing: normal;
        line-height: 1.15;
        color: var(--dl-color-theme-neutral-dark);
        background-color: var(--dl-color-theme-neutral-light);

        fill: var(--dl-color-theme-neutral-dark);
      }
    </style>
    <link
      rel="stylesheet"
      href="https://unpkg.com/animate.css@4.1.1/animate.css"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap"
      data-tag="font"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&amp;display=swap"
      data-tag="font"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&amp;display=swap"
      data-tag="font"
    />
    <link
      rel="stylesheet"
      href="https://unpkg.com/@teleporthq/teleport-custom-scripts/dist/style.css"
    />
    <style>
      @keyframes fade-in-left {
        0% {
          opacity: 0;
          transform: translateX(-20px);
        }
        100% {
          opacity: 1;
          transform: translateX(0);
        }
      }
    </style>
  </head>
  <body>
    <link rel="stylesheet" href="../style.css" />
    <div>
      <link href="./steps.css" rel="stylesheet" />
      <div class="steps-container thq-section-padding">
        <div class="steps-max-width thq-section-max-width">
          <div class="steps-container1 thq-grid-2">
            <div class="steps-section-header">
              <h2 class="thq-heading-2">Discover the Power of Our Products</h2>
              <p class="thq-body-large">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                Suspendisse varius enim in eros elementum tristique. Duis
                cursus, mi quis viverra ornare, eros dolor interdum nulla, ut
                commodo diam libero vitae erat.
              </p>
              <div class="steps-actions">
                <button
                  class="thq-button-filled thq-button-animated steps-button"
                >
                  <span class="thq-body-small">Main action</span>
                </button>
              </div>
            </div>
            <div class="steps-container2">
              <div class="steps-container3 thq-card">
                <h2 class="thq-heading-2">
                  <span>Sign Up for an Account</span>
                </h2>
                <span class="steps-text04 thq-body-small">
                  <span>
                    Create a new account by providing your email address and
                    setting up a password.
                  </span>
                </span>
                <label class="steps-text05 thq-heading-3">01</label>
              </div>
              <div class="steps-container4 thq-card">
                <h2 class="thq-heading-2"><span>Search for Products</span></h2>
                <span class="steps-text07 thq-body-small">
                  <span>
                    Use the search bar to find the product you want to read
                    reviews about.
                  </span>
                </span>
                <label class="steps-text08 thq-heading-3">02</label>
              </div>
              <div class="steps-container5 thq-card">
                <h2 class="thq-heading-2"><span>Read Short Reviews</span></h2>
                <span class="steps-text10 thq-body-small">
                  <span>
                    Browse through concise reviews from various sites to get a
                    quick overview of the product.
                  </span>
                </span>
                <label class="steps-text11 thq-heading-3">03</label>
              </div>
              <div class="steps-container6 thq-card">
                <h2 class="thq-heading-2">
                  <span>Login to Access More Features</span>
                </h2>
                <span class="steps-text13 thq-body-small">
                  <span>
                    Login to your account to save products, write your own
                    reviews, and interact with other users.
                  </span>
                </span>
                <label class="steps-text14 thq-heading-3">04</label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script
      data-section-id="navbar"
      src="https://unpkg.com/@teleporthq/teleport-custom-scripts"
    ></script>
  </body>
</html>
