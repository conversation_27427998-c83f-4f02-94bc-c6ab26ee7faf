/* Enhanced Profile Page Styles */

/* Avatar Container Enhancements */
.avatar-container {
  position: relative;
  display: inline-block;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.change-avatar-btn {
  background: transparent;
  border: 2px solid white;
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-avatar-btn:hover {
  background: white;
  color: #333;
}

/* Online Status */
.online-status {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.status-indicator.online {
  background: #4CAF50;
}

/* Profile Stats Mini */
.profile-stats-mini {
  display: flex;
  gap: 20px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.mini-stat {
  font-size: 14px;
  color: #666;
}

.mini-stat strong {
  color: #333;
  font-weight: 600;
}

/* Profile Actions */
.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
}

.share-profile-btn {
  background: #1976D2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: background 0.3s ease;
}

.share-profile-btn:hover {
  background: #1565C0;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.section-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

/* Enhanced Stats Cards */
.stat-card.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-card .stat-icon {
  font-size: 24px;
  margin-bottom: 10px;
  color: #1976D2;
}

.stat-content {
  text-align: center;
}

.stat-value {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 10px 0;
}

.stat-value .number {
  font-size: 2em;
  font-weight: bold;
  color: #333;
}

.trend {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background: #E8F5E8;
  color: #4CAF50;
}

.stars {
  color: #FFD700;
}

.stat-subtitle {
  color: #666;
  font-size: 12px;
}

/* Search and Filter Controls */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  padding: 8px 35px 8px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
  width: 200px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #1976D2;
}

.search-icon {
  position: absolute;
  right: 12px;
  color: #666;
}

.filter-select, .sort-select, .time-filter {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

/* View Toggle */
.view-toggle {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  background: white;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.view-btn.active {
  background: #1976D2;
  color: white;
}

/* Products Grid/List Views */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.product-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.product-image {
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-view-btn {
  background: white;
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
}

.product-info {
  padding: 15px;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

.price {
  font-size: 18px;
  font-weight: bold;
  color: #1976D2;
}

.category {
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: #666;
}

.product-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.favorite-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #ccc;
  cursor: pointer;
  transition: color 0.3s ease;
}

.favorite-btn:hover,
.favorite-btn .icon-heart.favorited {
  color: #e91e63;
}

/* Review Tabs */
.review-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab-btn.active {
  border-bottom-color: #1976D2;
  color: #1976D2;
  font-weight: 500;
}

/* Review Cards */
.review-card {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  transition: box-shadow 0.3s ease;
}

.review-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.review-rating {
  color: #FFD700;
  font-size: 16px;
}

.review-date {
  color: #666;
  font-size: 12px;
}

.review-product {
  margin: 10px 0;
  color: #1976D2;
  font-size: 16px;
}

.review-content {
  color: #333;
  line-height: 1.5;
  margin-bottom: 10px;
}

.review-actions {
  display: flex;
  gap: 10px;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: #f5f5f5;
}

/* Progress Tracker */
.progress-container {
  margin-bottom: 15px;
}

.level-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  transition: width 0.5s ease;
}

.progress-text {
  display: block;
  text-align: center;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

/* Activity List */
.activity-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-content {
  flex: 1;
}

.activity-text {
  display: block;
  font-size: 13px;
  color: #333;
}

.activity-time {
  display: block;
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}

/* Live Updates */
.updates-container {
  max-height: 200px;
  overflow-y: auto;
}

.update-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  margin-bottom: 5px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 13px;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.update-time {
  margin-left: auto;
  font-size: 11px;
  color: #666;
}

/* Auto-refresh Toggle */
.auto-refresh {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
  font-size: 13px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #1976D2;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  margin-top: 20px;
}

.page-btn {
  background: white;
  border: 1px solid #ddd;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:hover:not(.disabled) {
  background: #f5f5f5;
}

.page-btn.active {
  background: #1976D2;
  color: white;
  border-color: #1976D2;
}

.page-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-ellipsis {
  padding: 8px 4px;
  color: #666;
}

/* Icon Styles */
.icon-camera::before { content: "📷"; }
.icon-location::before { content: "📍"; }
.icon-info::before { content: "ℹ️"; }
.icon-share::before { content: "🔗"; }
.icon-refresh::before { content: "🔄"; }
.icon-reviews::before { content: "📝"; }
.icon-star::before { content: "⭐"; }
.icon-products::before { content: "📦"; }
.icon-engagement::before { content: "📊"; }
.icon-search::before { content: "🔍"; }
.icon-grid::before { content: "⊞"; }
.icon-list::before { content: "☰"; }
.icon-plus::before { content: "➕"; }
.icon-heart::before { content: "♡"; }
.icon-heart.favorited::before { content: "❤️"; }
.icon-chevron-left::before { content: "‹"; }
.icon-chevron-right::before { content: "›"; }
.icon-review::before { content: "💬"; }
.icon-notifications::before { content: "🔔"; }
.icon-settings::before { content: "⚙️"; }
.icon-download::before { content: "⬇️"; }
.icon-login::before { content: "🔑"; }
.icon-profile::before { content: "👤"; }
.icon-success::before { content: "✅"; }
.icon-error::before { content: "❌"; }
.icon-warning::before { content: "⚠️"; }

/* Loading Animation */
.icon-loading::before {
  content: "⟳";
  animation: spin 1s linear infinite;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Button Tooltips */
.btn-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.action-btn:hover .btn-tooltip {
  opacity: 1;
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #e91e63;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* Modal Styles */
.modal {
  display: block;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  position: relative;
  animation: slideDown 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #666;
}

.close:hover {
  color: #333;
}

/* Form Styles */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #1976D2;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

/* Star Rating */
.star-rating {
  display: flex;
  gap: 2px;
  margin: 5px 0;
}

.star {
  font-size: 20px;
  color: #ddd;
  cursor: pointer;
  transition: color 0.2s ease;
}

.star:hover,
.star.active {
  color: #FFD700;
}

/* Enhanced Action Buttons */
.action-btn {
  position: relative;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #333;
  font-size: 14px;
}

.action-btn:hover {
  background: #f5f5f5;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-btn.primary {
  background: #1976D2;
  color: white;
  border-color: #1976D2;
}

.action-btn.primary:hover {
  background: #1565C0;
}

/* No Data States */
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: stretch;
  }

  .section-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .profile-actions {
    flex-direction: row;
    justify-content: center;
  }

  .search-input {
    width: 150px;
  }

  .modal-content {
    margin: 10% auto;
    width: 95%;
  }

  .profile-stats-mini {
    justify-content: center;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }
}
