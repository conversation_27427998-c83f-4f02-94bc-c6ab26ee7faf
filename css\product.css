/* Product Page Styles */
.product-page {
  min-height: 100vh;
  background-color: var(--dl-color-theme-secondary1);
  background-image: url('/images/Index/monaco-iphone-wallpaper-picjumbo-com.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: scroll;
  background-blend-mode: soft-light;
  padding-top: 80px; /* Account for fixed header */
}

/* Product Header Section */
.product-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--dl-radius-radius-cardradius);
  box-shadow: var(--shadow-lg);
}

.product-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--dl-space-space-fiveunits);
  align-items: start;
}

.product-image {
  position: relative;
}

.product-image img {
  width: 100%;
  max-width: 500px;
  height: 400px;
  object-fit: cover;
  border-radius: var(--dl-radius-radius-imageradius);
  box-shadow: var(--shadow-lg);
}

.image-gallery {
  display: flex;
  gap: var(--dl-space-space-halfunit);
  margin-top: var(--dl-space-space-unit);
  justify-content: center;
}

.gallery-btn {
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--dl-radius-radius-imageradius);
  overflow: hidden;
  transition: transform var(--transition-base);
}

.gallery-btn:hover {
  transform: scale(1.05);
}

.gallery-btn img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--dl-radius-radius-imageradius);
}

.product-details h1 {
  color: var(--dl-color-theme-neutral-dark);
  margin-bottom: var(--dl-space-space-unit);
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--dl-space-space-oneandhalfunits);
}

.product-category {
  background: var(--dl-color-theme-accent1);
  color: var(--dl-color-theme-primary1);
  padding: var(--dl-space-space-halfunit) var(--dl-space-space-unit);
  border-radius: var(--dl-radius-radius-buttonradius);
  font-size: 0.9rem;
  font-weight: 500;
}

.product-price {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dl-color-theme-primary1);
}

.product-rating-summary {
  display: flex;
  align-items: center;
  gap: var(--dl-space-space-twounits);
  margin-bottom: var(--dl-space-space-twounits);
  padding: var(--dl-space-space-unit);
  background: var(--dl-color-theme-secondary2);
  border-radius: var(--dl-radius-radius-cardradius);
}

.overall-rating {
  display: flex;
  align-items: center;
  gap: var(--dl-space-space-halfunit);
}

.rating-score {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dl-color-theme-primary1);
}

.rating-stars {
  font-size: 1.5rem;
  color: var(--dl-color-theme-primary1);
}

.rating-count {
  color: var(--dl-color-theme-neutral-dark);
  font-size: 0.9rem;
}

.product-description {
  color: var(--dl-color-theme-neutral-dark);
}

/* AI Analysis Section */
.ai-analysis-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--dl-radius-radius-cardradius);
  box-shadow: var(--shadow-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--dl-space-space-twounits);
}

.section-header h2 {
  color: var(--dl-color-theme-neutral-dark);
  margin: 0;
}

.analysis-grid {
  gap: var(--dl-space-space-twounits);
}

.analysis-card {
  background: var(--dl-color-theme-neutral-light);
  border-radius: var(--dl-radius-radius-cardradius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--dl-color-theme-secondary2);
  transition: transform var(--transition-base), box-shadow var(--transition-base);
}

.analysis-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.analysis-card h3 {
  color: var(--dl-color-theme-neutral-dark);
  border-bottom: 2px solid var(--dl-color-theme-secondary2);
  padding-bottom: var(--dl-space-space-halfunit);
  margin-bottom: var(--dl-space-space-oneandhalfunits);
}

/* Summary Card */
.summary-card {
  grid-column: span 2;
}

.summary-content {
  color: var(--dl-color-theme-neutral-dark);
}

.placeholder-text {
  color: var(--color-text-muted);
  font-style: italic;
}

/* Pros and Cons Card */
.pros-cons-grid {
  gap: var(--dl-space-space-twounits);
}

.pros-section ul,
.cons-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pros-section li,
.cons-section li {
  padding: var(--dl-space-space-halfunit) 0;
  border-bottom: 1px solid var(--dl-color-theme-secondary2);
  color: var(--dl-color-theme-neutral-dark);
}

.pros-section li:last-child,
.cons-section li:last-child {
  border-bottom: none;
}

/* Sentiment Analysis */
.sentiment-chart {
  margin-top: var(--dl-space-space-unit);
}

.sentiment-bar {
  display: flex;
  height: 60px;
  border-radius: var(--dl-radius-radius-buttonradius);
  overflow: hidden;
  background: var(--dl-color-theme-secondary2);
  position: relative;
}

.sentiment-segment {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--dl-color-theme-neutral-light);
  font-weight: 600;
  font-size: 0.9rem;
  transition: width 0.8s ease;
  position: relative;
}

.sentiment-segment.positive {
  background: linear-gradient(135deg, var(--dl-color-theme-primary1), var(--dl-color-theme-accent1));
}

.sentiment-segment.neutral {
  background: linear-gradient(135deg, var(--dl-color-theme-accent2), var(--dl-color-theme-secondary1));
}

.sentiment-segment.negative {
  background: linear-gradient(135deg, #f44336, #ef5350);
}

.sentiment-label {
  font-size: 0.8rem;
  opacity: 0.9;
}

.sentiment-percentage {
  font-weight: 700;
  font-size: 1rem;
}

/* Rating Analysis */
.rating-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--dl-space-space-unit);
}

.rating-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--dl-space-space-halfunit);
  background: var(--dl-color-theme-secondary1);
  border-radius: var(--dl-radius-radius-cardradius);
}

.rating-item span:first-child {
  font-weight: 500;
  color: var(--dl-color-theme-neutral-dark);
}

.rating-item span:last-child {
  font-weight: 600;
  color: var(--dl-color-theme-neutral-dark);
}

/* Aspect Ratings */
.aspect-ratings {
  display: flex;
  flex-direction: column;
  gap: var(--dl-space-space-oneandhalfunits);
}

.aspect-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.aspect-label {
  font-weight: 500;
  color: var(--dl-color-theme-neutral-dark);
  min-width: 120px;
}

.aspect-rating {
  display: flex;
  align-items: center;
  gap: var(--dl-space-space-unit);
}

.aspect-stars {
  font-size: 1.2rem;
  color: var(--dl-color-theme-primary1);
  cursor: default;
}

.aspect-score {
  font-weight: 600;
  color: var(--dl-color-theme-neutral-dark);
  min-width: 40px;
}

/* Reviews Section */
.reviews-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--dl-radius-radius-cardradius);
  box-shadow: var(--shadow-lg);
}

.review-controls {
  display: flex;
  gap: var(--dl-space-space-unit);
}

.reviews-container {
  margin-top: var(--dl-space-space-twounits);
}

.review-item {
  background: var(--dl-color-theme-neutral-light);
  border-radius: var(--dl-radius-radius-cardradius);
  padding: var(--dl-space-space-oneandhalfunits);
  margin-bottom: var(--dl-space-space-oneandhalfunits);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--dl-color-theme-secondary2);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--dl-space-space-unit);
}

.review-author {
  font-weight: 600;
  color: var(--dl-color-theme-neutral-dark);
}

.review-date {
  color: var(--color-text-muted);
  font-size: 0.9rem;
}

.review-rating {
  color: var(--dl-color-theme-primary1);
  font-size: 1.1rem;
}

.review-content {
  color: var(--dl-color-theme-neutral-dark);
  margin-bottom: var(--dl-space-space-unit);
}

.review-source {
  display: inline-block;
  background: var(--dl-color-theme-accent1);
  color: var(--dl-color-theme-primary1);
  padding: var(--dl-space-space-halfunit) var(--dl-space-space-unit);
  border-radius: var(--dl-radius-radius-buttonradius);
  font-size: 0.8rem;
  font-weight: 500;
}

.loading-reviews {
  text-align: center;
  padding: var(--dl-space-space-fiveunits);
  color: var(--color-text-muted);
}

.load-more-container {
  text-align: center;
  margin-top: var(--dl-space-space-twounits);
}

/* Loading States */
.btn-loading {
  opacity: 0.7;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--dl-color-theme-secondary2);
  border-top: 2px solid var(--dl-color-theme-primary1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .summary-card {
    grid-column: span 1;
  }

  .pros-cons-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .product-info {
    grid-template-columns: 1fr;
    gap: var(--dl-space-space-twounits);
  }

  .product-rating-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--dl-space-space-unit);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--dl-space-space-unit);
  }

  .review-controls {
    flex-direction: column;
  }

  .sentiment-bar {
    height: 80px;
  }

  .sentiment-segment {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .product-page {
    padding-top: 60px;
  }

  .product-price {
    font-size: 1.5rem;
  }
}

/* Interactive Features */
.filter-active {
  background: #007bff !important;
  color: white !important;
}

.analysis-loading {
  opacity: 0.6;
  pointer-events: none;
}

.analysis-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.review-highlight {
  background: #fff3cd;
  border-left: 4px solid #ffc107;
  transition: all 0.3s ease;
}

.sentiment-tooltip {
  position: relative;
  cursor: help;
}

.sentiment-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.sentiment-tooltip:hover::after {
  opacity: 1;
}

.aspect-progress {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.aspect-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #f44336, #ff9800, #4caf50);
  border-radius: 4px;
  transition: width 0.8s ease;
}

.review-source-filter {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.source-tag {
  padding: 0.25rem 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.source-tag:hover,
.source-tag.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.analysis-export {
  margin-top: 1rem;
  text-align: center;
}

.export-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.3s ease;
}

.export-btn:hover {
  background: #218838;
}

/* Animation for analysis cards */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.analysis-card.animate {
  animation: slideInUp 0.6s ease forwards;
}

.analysis-card.animate:nth-child(1) { animation-delay: 0.1s; }
.analysis-card.animate:nth-child(2) { animation-delay: 0.2s; }
.analysis-card.animate:nth-child(3) { animation-delay: 0.3s; }
.analysis-card.animate:nth-child(4) { animation-delay: 0.4s; }
.analysis-card.animate:nth-child(5) { animation-delay: 0.5s; }
.analysis-card.animate:nth-child(6) { animation-delay: 0.6s; }

/* Error states */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #c3e6cb;
}

/* Modal enhancements */
.modal {
  backdrop-filter: blur(5px);
}

.modal-content {
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
