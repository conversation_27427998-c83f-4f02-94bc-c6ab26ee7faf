<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Spotless Hungry Crocodile</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta charset="utf-8" />
    <meta property="twitter:card" content="summary_large_image" />

    <style data-tag="reset-style-sheet">
      html {  line-height: 1.15;}body {  margin: 0;}* {  box-sizing: border-box;  border-width: 0;  border-style: solid;}p,li,ul,pre,div,h1,h2,h3,h4,h5,h6,figure,blockquote,figcaption {  margin: 0;  padding: 0;}button {  background-color: transparent;}button,input,optgroup,select,textarea {  font-family: inherit;  font-size: 100%;  line-height: 1.15;  margin: 0;}button,select {  text-transform: none;}button,[type="button"],[type="reset"],[type="submit"] {  -webkit-appearance: button;}button::-moz-focus-inner,[type="button"]::-moz-focus-inner,[type="reset"]::-moz-focus-inner,[type="submit"]::-moz-focus-inner {  border-style: none;  padding: 0;}button:-moz-focus,[type="button"]:-moz-focus,[type="reset"]:-moz-focus,[type="submit"]:-moz-focus {  outline: 1px dotted ButtonText;}a {  color: inherit;  text-decoration: inherit;}input {  padding: 2px 4px;}img {  display: block;}html { scroll-behavior: smooth  }
    </style>
    <style data-tag="default-style-sheet">
      html {
        font-family: Inter;
        font-size: 16px;
      }

      body {
        font-weight: 400;
        font-style:normal;
        text-decoration: none;
        text-transform: none;
        letter-spacing: normal;
        line-height: 1.15;
        color: var(--dl-color-theme-neutral-dark);
        background-color: var(--dl-color-theme-neutral-light);

        fill: var(--dl-color-theme-neutral-dark);
      }
    </style>
    <link
      rel="stylesheet"
      href="https://unpkg.com/animate.css@4.1.1/animate.css"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap"
      data-tag="font"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&amp;display=swap"
      data-tag="font"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&amp;display=swap"
      data-tag="font"
    />
    <link
      rel="stylesheet"
      href="https://unpkg.com/@teleporthq/teleport-custom-scripts/dist/style.css"
    />
    <style>
      @keyframes fade-in-left {
        0% {
          opacity: 0;
          transform: translateX(-20px);
        }
        100% {
          opacity: 1;
          transform: translateX(0);
        }
      }
    </style>
  </head>
  <body>
    <link rel="stylesheet" href="../style.css" />
    <div>
      <link href="./navbar.css" rel="stylesheet" />
      <header class="navbar-container">
        <header data-thq="thq-navbar" class="navbar-navbar-interactive">
          <img
            alt="Feedback Hub Logo"
            src="public/screenshot%202024-07-05%20183621-1400w.png"
            class="navbar-image1"
          />
          <div data-thq="thq-navbar-nav" class="navbar-desktop-menu">
            <nav class="navbar-links">
              <span class="thq-link thq-body-small"><span>Home</span></span>
              <span class="thq-link thq-body-small"><span>Reviews</span></span>
              <span class="thq-link thq-body-small"><span>About Us</span></span>
              <span class="thq-link thq-body-small">
                <span>Contact Us</span>
              </span>
            </nav>
            <div class="navbar-buttons">
              <button
                class="navbar-action1 thq-button-filled thq-button-animated"
              >
                <span class="thq-body-small">Log in</span>
              </button>
              <button
                class="navbar-action2 thq-button-outline thq-button-animated"
              >
                <span class="thq-body-small">Sign Up</span>
              </button>
            </div>
          </div>
          <div data-thq="thq-burger-menu" class="navbar-burger-menu">
            <svg viewBox="0 0 1024 1024" class="navbar-icon">
              <path
                d="M128 554.667h768c23.552 0 42.667-19.115 42.667-42.667s-19.115-42.667-42.667-42.667h-768c-23.552 0-42.667 19.115-42.667 42.667s19.115 42.667 42.667 42.667zM128 298.667h768c23.552 0 42.667-19.115 42.667-42.667s-19.115-42.667-42.667-42.667h-768c-23.552 0-42.667 19.115-42.667 42.667s19.115 42.667 42.667 42.667zM128 810.667h768c23.552 0 42.667-19.115 42.667-42.667s-19.115-42.667-42.667-42.667h-768c-23.552 0-42.667 19.115-42.667 42.667s19.115 42.667 42.667 42.667z"
              ></path>
            </svg>
          </div>
          <div data-thq="thq-mobile-menu" class="navbar-mobile-menu">
            <div class="navbar-nav">
              <div class="navbar-top">
                <img
                  alt="Feedback Hub Logo"
                  src="public/screenshot%202024-07-05%20183621-1400w.png"
                  class="navbar-logo"
                />
                <div data-thq="thq-close-menu" class="navbar-close-menu">
                  <svg viewBox="0 0 1024 1024" class="navbar-icon2">
                    <path
                      d="M810 274l-238 238 238 238-60 60-238-238-238 238-60-60 238-238-238-238 60-60 238 238 238-238z"
                    ></path>
                  </svg>
                </div>
              </div>
              <nav class="navbar-links1">
                <span class="thq-link thq-body-small"><span>Home</span></span>
                <span class="thq-link thq-body-small">
                  <span>Reviews</span>
                </span>
                <span class="thq-link thq-body-small">
                  <span>About Us</span>
                </span>
                <span class="thq-link thq-body-small">
                  <span>Contact Us</span>
                </span>
                <span class="thq-link thq-body-small"><span>Link5</span></span>
              </nav>
            </div>
            <div class="navbar-buttons1">
              <button class="thq-button-filled">Login</button>
              <button class="thq-button-outline">Register</button>
            </div>
          </div>
        </header>
      </header>
    </div>
    <script
      data-section-id="navbar"
      src="https://unpkg.com/@teleporthq/teleport-custom-scripts"
    ></script>
  </body>
</html>
