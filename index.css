.steps-container {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-color: #f5f8f9e6;
  background-image: url('/images/Index/monaco-iphone-wallpaper-picjumbo-com.jpg');
  background-blend-mode: soft-light;

}

.navbar-container {
  width: 100%;
  display: flex;
  position: relative;
  justify-content: center;
  background-color: var(--dl-color-theme-neutral-light);
}

.navbar-navbar-interactive {
  width: 100%;
  display: flex;
  max-width: var(--dl-size-size-maxwidth);
  align-items: center;
  padding-top: var(--dl-space-space-twounits);
  padding-left: var(--dl-space-space-threeunits);
  padding-right: var(--dl-space-space-threeunits);
  padding-bottom: var(--dl-space-space-twounits);
  justify-content: space-between;
}

.navbar-desktop-menu {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.navbar-links {
  gap: var(--dl-space-space-twounits);
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: var(--dl-space-space-twounits);
  flex-direction: row;
  justify-content: flex-start;
}

.navbar-buttons {
  gap: var(--dl-space-space-twounits);
  display: flex;
  align-items: center;
  margin-left: var(--dl-space-space-twounits);
  border-radius: var(--radius-base);
}

.navbar-burger-menu {
  display: none;
}

.navbar-icon {
  width: var(--dl-size-size-xsmall);
  height: var(--dl-size-size-xsmall);
}

.navbar-mobile-menu {
  top: 0px;
  right: 0px;
  width: 20%;
  height: 100vh;
  display: none;
  padding: var(--dl-space-space-twounits);
  z-index: 10;
  position: absolute;
  flex-direction: column;
  background-color: var(--dl-color-theme-neutral-light);
}

.navbar-nav {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.navbar-top {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: var(--dl-space-space-threeunits);
  justify-content: space-between;
}

.navbar-logo {
  height: 3rem;
}

.navbar-close-menu {
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-icon2 {
  width: var(--dl-size-size-xsmall);
  height: var(--dl-size-size-xsmall);
}

.navbar-links1 {
  gap: var(--dl-space-space-unit);
  flex: 0 0 auto;
  display: flex;
  align-self: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

.navbar-buttons1 {
  gap: var(--dl-space-space-twounits);
  display: flex;
  margin-top: var(--dl-space-space-twounits);
  align-items: center;
  border-radius: var(--radius-base);
}

@media(max-width: 767px) {
  .navbar-navbar-interactive {
    padding-left: var(--dl-space-space-twounits);
    padding-right: var(--dl-space-space-twounits);
  }

  .navbar-desktop-menu {
    display: none;
  }

  .navbar-burger-menu {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@media(max-width: 479px) {
  .navbar-navbar-interactive {
    padding: var(--dl-space-space-unit);
  }

  .navbar-mobile-menu {
    padding: var(--dl-space-space-unit);
  }
}

.hero-header {
  display: flex;
  padding: 200px 200px 50px 200px;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  gap: var(--line-height-24, 25px);
  align-items: center;
  align-self: stretch;
  background-size: cover;
  background-position: center;
  background-image: url(/images/Index/background.png);
  /* background-blend-mode: overlay; */
}

.about-us {
  display: flex;
  padding: 200px 200px 50px 200px;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  gap: var(--line-height-24, 25px);
  align-items: center;
  align-self: stretch;
  background-size: cover;
  background-position: center;
  /* background-image: url('/images/Index/monaco-iphone-wallpaper-picjumbo-com.jpg'); */
  background-color: #faf9f6b4;
  background-blend-mode: overlay;
}

.hero-column {
  gap: var(--dl-space-space-oneandhalfunits);
  width: auto;
  display: flex;
  align-items: center;
  flex-direction: column;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.60);
  box-shadow: 0px 10px 10px 0px rgba(0, 0, 0, 0.25);
  padding-bottom: var(--dl-space-space-unit);
}

.hero-content {
  gap: var(--dl-space-space-oneandhalfunits);
  display: flex;
  align-self: stretch;
  align-items: center;
  flex-direction: column;
}

.hero-text {
  text-align: center;
}

.hero-text1 {
  text-align: center;
}

.hero-actions {
  gap: var(--dl-space-space-unit);
  display: flex;
  align-items: flex-start;
  padding-top: var(--dl-space-space-unit);
}

.hero-content1 {
  gap: var(--dl-space-space-oneandhalfunits);
  width: 100%;
  display: flex;
  position: relative;
  align-items: flex-start;
  flex-direction: column;
}

.hero-row-container {
  width: 100%;
}

.hero-placeholder-image {
  width: 400px;
  height: 400px;
}

.hero-container1 {
  display: flex;

}

@keyframes scroll-x {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-100% - 16px));
  }
}

@keyframes scroll-y {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(calc(-100% - 16px));
  }
}

@media(max-width: 767px) {
  .hero-content1 {
    width: 100%;
  }
}

@media(max-width: 479px) {
  .hero-actions {
    width: 100%;
    flex-direction: column;
  }

  .hero-button {
    width: 100%;
  }

  .hero-button1 {
    width: 100%;
  }
}

.features1-container1 {
  width: 100%;
  display: grid;
  grid-gap: var(--dl-space-space-fiveunits);
  position: relative;
  grid-template-columns: var(--dl-grid-template-columns, 1fr 1fr);
}

.features1-image-container {
  height: 100%;
  display: flex;
  position: relative;
}

.features1-image {
  animation-name: fadeIn;
  animation-delay: 1s;
  animation-duration: 1s;
  animation-direction: normal;
  animation-iteration-count: 0;
  animation-timing-function: ease;
}

.features1-tabs-menu {
  gap: var(--dl-space-space-twounits);
  width: 100%;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
}

.features1-tab-horizontal {
  gap: var(--dl-space-space-twounits);
  cursor: pointer;
  display: flex;
  overflow: hidden;
  align-self: stretch;
  align-items: flex-start;
  flex-shrink: 0;
}

.features1-divider-container {
  display: flex;
  align-self: stretch;
  align-items: flex-start;
}

.features1-container2 {
  width: 2px;
  align-self: stretch;
  background-color: var(--dl-color-theme-neutral-dark);
}

.features1-content {
  gap: 16px;
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-grow: 1;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: center;
}

.features1-tab-horizontal1 {
  gap: var(--dl-space-space-twounits);
  cursor: pointer;
  display: flex;
  overflow: hidden;
  align-self: stretch;
  align-items: flex-start;
  flex-shrink: 0;
}

.features1-content1 {
  gap: 16px;
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-grow: 1;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: center;
}

.features1-tab-horizontal2 {
  gap: var(--dl-space-space-twounits);
  cursor: pointer;
  display: flex;
  overflow: hidden;
  align-self: stretch;
  align-items: flex-start;
  flex-shrink: 0;
}

.features1-content2 {
  gap: 16px;
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-grow: 1;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: center;
}

@media(max-width: 991px) {
  .features1-container1 {
    grid-gap: var(--dl-space-space-twounits);
    grid-template-columns: 1fr;
  }
}

.cta-accent2-bg {
  gap: var(--dl-space-space-oneandhalfunits);
  display: flex;
  transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(1deg) skew(0deg, 0deg);
  align-self: stretch;
  transition: 0.3s;
  align-items: center;
  border-radius: var(--dl-radius-radius-cardradius);
  justify-content: space-between;
  transform-style: preserve-3d;
  background-color: var(--dl-color-theme-accent2);
}

.cta-accent2-bg:hover {
  transform: scale3d(1.1, 1.1, 1.1);
}

.cta-accent1-bg {
  width: 100%;
  display: flex;
  transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(-2deg) skew(0deg, 0deg);
  align-items: center;
  border-radius: var(--dl-radius-radius-cardradius);
  justify-content: space-between;
  transform-style: preserve-3d;
  background-color: var(--dl-color-theme-accent1);
}

.cta-container1 {
  gap: var(--dl-space-space-threeunits);
  width: 100%;
  display: flex;
  transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(1deg) skew(0deg, 0deg);
  transition: 0.2s;
  align-items: center;
  padding-top: var(--dl-space-space-sixunits);
  padding-left: var(--dl-space-space-fourunits);
  border-radius: var(--dl-radius-radius-cardradius);
  padding-right: var(--dl-space-space-fourunits);
  padding-bottom: var(--dl-space-space-sixunits);
}

.cta-container1:hover {
  color: var(--dl-color-theme-neutral-dark);
  /* background-color: var(--dl-color-theme-accent1); */
}

.cta-content {
  gap: var(--dl-space-space-oneandhalfunits);
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.cta-actions {
  gap: var(--dl-space-space-oneandhalfunits);
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;

}

@media(max-width: 767px) {
  .cta-container1 {
    gap: var(--dl-space-space-oneandhalfunits);
    flex-direction: column;
    justify-content: flex-start;
  }
}

@media(max-width: 479px) {
  .cta-actions {
    flex-wrap: wrap;
    align-self: stretch;
    justify-content: center;
  }

  .cta-button {
    flex: 1;
  }
}

.features2-container1 {
  width: 100%;
  display: grid;
  grid-gap: var(--dl-space-space-fiveunits);
  position: relative;
  grid-template-columns: 1fr 1fr;
}

.features2-tabs-menu {
  gap: var(--dl-space-space-twounits);
  width: 100%;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
}

.features2-tab-horizontal {
  gap: var(--dl-space-space-twounits);
  cursor: pointer;
  display: flex;
  overflow: hidden;
  align-self: stretch;
  align-items: flex-start;
  flex-shrink: 0;
}

.features2-divider-container {
  display: flex;
  align-self: stretch;
  align-items: flex-start;
}

.features2-container2 {
  width: 2px;
  align-self: stretch;
  background-color: var(--dl-color-theme-neutral-dark);
}

.features2-content {
  gap: 16px;
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-grow: 1;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: center;
}

.features2-tab-horizontal1 {
  gap: var(--dl-space-space-twounits);
  cursor: pointer;
  display: flex;
  overflow: hidden;
  align-self: stretch;
  align-items: flex-start;
  flex-shrink: 0;
}

.features2-divider-container1 {
  display: flex;
  align-self: stretch;
  align-items: flex-start;
}

.features2-container3 {
  width: 2px;
  align-self: stretch;
  background-color: var(--dl-color-theme-neutral-dark);
}

.features2-content1 {
  gap: 16px;
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-grow: 1;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: center;
}

.features2-tab-horizontal2 {
  gap: var(--dl-space-space-twounits);
  cursor: pointer;
  display: flex;
  overflow: hidden;
  align-self: stretch;
  align-items: flex-start;
  flex-shrink: 0;
}

.features2-divider-container2 {
  display: flex;
  align-self: stretch;
  align-items: flex-start;
}

.features2-container4 {
  width: 2px;
  align-self: stretch;
  background-color: var(--dl-color-theme-neutral-dark);
}

.features2-content2 {
  gap: 16px;
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-grow: 1;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: center;
}

.features2-image-container {
  height: 100%;
  display: flex;
  position: relative;
}


@media(max-width: 991px) {
  .features2-container1 {
    grid-gap: var(--dl-space-space-twounits);
    grid-template-columns: 1fr;
  }

  .features2-tabs-menu {
    order: 2;
  }
}

.steps-container {
  width: 100%;
  display: flex;
  position: relative;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.steps-max-width {
  gap: var(--dl-space-space-fourunits);
  width: 100%;
  display: flex;
  align-items: flex-start;
  flex-direction: row;
}

.steps-section-header {
  gap: var(--dl-space-space-oneandhalfunits);
  top: 30%;
  display: flex;
  position: sticky;
  align-items: flex-start;
  flex-direction: column;
}

.steps-actions {
  gap: var(--dl-space-space-unit);
  display: flex;
  align-items: flex-start;
}

.steps-container1 {
  align-items: start;
}

.steps-container2 {
  grid-area: span 1/span 1/span 1/span 1;
}

.steps-container3 {
  top: 10%;
  position: sticky;
  transform: rotate(-2deg);
  margin-bottom: var(--dl-space-space-twounits);
  background-color: var(--dl-color-theme-accent1);
}

.steps-container4 {
  top: 10%;
  position: sticky;
  transform: rotate(2deg);
  margin-bottom: var(--dl-space-space-twounits);
  background-color: var(--dl-color-theme-accent2);
}

.steps-text01 {
  text-align: center;
}

.steps-text02 {
  top: var(--dl-space-space-unit);
  right: var(--dl-space-space-unit);
  position: absolute;
  font-size: 40px;
  font-style: normal;
  font-weight: 700;
}

@media(max-width: 991px) {
  .steps-max-width {
    flex-direction: column;
  }
}

@media(max-width: 767px) {
  .steps-section-header {
    position: static;
    margin-bottom: var(--dl-space-space-twounits);
  }

  .steps-actions {
    width: 100%;
    align-self: flex-start;
  }

  .steps-container3 {
    width: 100%;
  }

  .steps-container4 {
    width: 100%;
  }

  .steps-container5 {
    width: 100%;
  }

  .steps-container6 {
    width: 100%;
  }
}

@media(max-width: 479px) {
  .steps-button {
    width: 100%;
  }
}

.testimonial-max-width {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.testimonial-container {
  gap: var(--dl-space-space-unit);
  display: flex;
  max-width: 600px;
  align-items: center;
  margin-bottom: var(--dl-space-space-fourunits);
  flex-direction: column;
}

.testimonial-text01 {
  text-align: left;
}

.testimonial-container01 {
  gap: var(--dl-space-space-unit);
  display: flex;
  align-self: flex-start;
  align-items: center;
  flex-direction: row;
  justify-content: center;
}

.testimonial-image {
  width: var(--dl-size-size-small);
  height: var(--dl-size-size-small);
  object-fit: cover;
  border-radius: var(--dl-radius-radius-round);
}

.testimonial-container02 {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
}

.testimonial-image2 {
  width: var(--dl-size-size-small);
  height: var(--dl-size-size-small);
  object-fit: cover;
  border-radius: var(--dl-radius-radius-round);
}

@media(max-width: 991px) {
  .testimonial-container {
    margin-bottom: var(--dl-space-space-threeunits);
  }
}

@media(max-width: 767px) {
  .testimonial-container {
    margin-bottom: var(--dl-space-space-oneandhalfunits);
  }

}

.contact-contact20 {
  display: flex;
  overflow: hidden;
  position: relative;
  align-items: center;
  flex-direction: column;
}

.contact-max-width {
  gap: var(--dl-space-space-twounits);
  display: flex;
  align-items: center;
  flex-direction: column;
}

.contact-section-title {
  gap: var(--dl-space-space-unit);
  width: auto;
  display: flex;
  max-width: 800px;
  align-items: center;
  flex-shrink: 0;
  flex-direction: column;
}

.contact-content {
  gap: var(--dl-space-space-oneandhalfunits);
  display: flex;
  align-self: stretch;
  align-items: center;
  flex-direction: column;
}

.contact-content:hover {
  .icon-medium {
    transform: scale(1.5);
  }
}

.contact-text {
  text-align: center;
  align-self: stretch;
}

.contact-row {
  gap: var(--dl-space-space-threeunits);
  display: flex;
  align-self: stretch, center;
  align-items: flex-start;
  padding: 10px;
}

.contact-contact-info {
  gap: var(--dl-space-space-oneandhalfunits);
  display: flex;
  align-self: stretch;
  align-items: center;
  flex-direction: column;
}

.contact-email {
  text-align: center;
}

.contact-contact-info1 {
  gap: var(--dl-space-space-oneandhalfunits);
  display: flex;
  align-self: stretch;
  align-items: center;
  flex-direction: column;
}

.contact-phone {
  text-align: center;
}

.contact-contact-info2 {
  gap: var(--dl-space-space-oneandhalfunits);
  display: flex;
  align-self: stretch;
  align-items: center;
  flex-direction: column;
}

.contact-address {
  text-align: center;
}

@media(max-width: 767px) {
  .contact-row {
    flex-direction: column;
  }
}

@media(max-width: 479px) {
  .contact-row {
    align-items: stretch;
  }
}

.footer {
  width: 100%;
  height: auto;
  display: flex;
  overflow: hidden;
  position: relative;
  align-items: center;
  flex-shrink: auto;
  flex-direction: column;
  justify-content: center;
  background-color: #faf9f6;
  align-self: stretch;
  padding: 0px 0px 0px 0px;

}

.footer-max-width {
  width: 100%;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.footer-content {
  gap: var(--dl-space-space-fourunits);
  width: 100%;
  display: flex;
  align-self: stretch;
  align-items: flex-start;
  flex-shrink: 0;
  border-radius: var(--dl-radius-radius-radius4);
}

.footer-newsletter {
  gap: 24px;
  width: 500px;
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
}

.footer-image {
  width: 148px;
  height: 67px;
}

.footer-actions {
  gap: 16px;
  width: 100%;
  display: flex;
  align-self: stretch;
  align-items: flex-start;
  flex-direction: column;
}

.footer-form {
  gap: var(--dl-space-space-unit);
  width: 100%;
  display: flex;
  align-self: stretch;
  align-items: stretch;
  flex-shrink: 0;
}

.footer-container {
  width: 365px;
  display: flex;
  align-items: flex-start;
}

.footer-text-input {
  gap: 8px;
  width: 100%;
  height: 32px;
  display: flex;
  font-size: 16px;
  box-sizing: content-box;
  text-align: left;
  align-items: center;
  font-family: Roboto;
  font-weight: 400;
  background-color: transparent;
}

.footer-content2 {
  fill: var(--dl-color-theme-neutral-dark);
  color: var(--dl-color-theme-neutral-dark);
  height: auto;
  font-size: 12px;
  align-self: stretch;
  font-style: Regular;
  text-align: left;
  font-family: "Roboto";
  font-weight: 400;
  line-height: 150%;
  font-stretch: normal;
  text-decoration: none;
}

.footer-links {
  gap: var(--dl-space-space-twounits);
  width: 100%;
  display: flex;
  flex-grow: 1;
  align-items: flex-start;
  justify-content: flex-end;
}

.footer-column {
  gap: var(--dl-space-space-unit);
  width: auto;
  display: flex;
  overflow: hidden;
  flex-grow: 1;
  max-width: 300px;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
}

.footer-footer-links {
  gap: var(--dl-space-space-halfunit);
  display: flex;
  align-self: stretch;
  align-items: flex-start;
  flex-direction: column;
}

.footer-social-links {
  gap: var(--dl-space-space-halfunit);
  display: flex;
  align-self: stretch;
  align-items: flex-start;
  flex-direction: column;
}

.footer-link {
  gap: 12px;
  display: flex;
  padding: 8px 0;
  align-self: stretch;
  align-items: center;
  flex-shrink: 0;
}

.footer-credits {
  gap: 0px;
  width: 100%;
  display: flex;
  align-self: stretch;
  margin-top: 0px;
  align-items: flex-start;
  flex-direction: column;
}

.footer-row {
  gap: 64px;
  display: flex;
  align-self: stretch;
  align-items: flex-start;
  flex-shrink: 0;
  justify-content: space-between;
}

@media(max-width: 991px) {
  .footer-newsletter {
    width: 300px;
  }

  .footer-form {
    width: 100%;
    flex-direction: column;
  }

  .footer-container {
    width: 100%;
  }

  .footer-text-input {
    width: 100%;
    padding: var(--dl-space-space-halfunit);
  }

  .footer-button {
    width: 100%;
    padding-top: var(--dl-space-space-halfunit);
    padding-left: var(--dl-space-space-halfunit);
    padding-right: var(--dl-space-space-halfunit);
    padding-bottom: var(--dl-space-space-halfunit);
  }
}

@media(max-width: 767px) {
  .footer-content {
    flex-direction: column;
  }

  .footer-newsletter {
    width: 100%;
  }

  .footer-form {
    width: 100%;
    flex-direction: row;
    justify-content: flex-start;
  }

  .footer-container {
    width: 100%;
  }

  .footer-button {
    width: 208px;
  }

  .footer-links {
    width: 100%;
    align-items: flex-start;
    justify-content: center;
  }

  .footer-column1 {
    align-items: center;
  }

  .footer-column1-title {
    text-align: center;
  }

  .footer-footer-links {
    align-self: center;
  }

  .footer-column2 {
    align-items: center;
  }

  .footer-column2-title {
    text-align: center;
  }

  .footer-column3 {
    align-items: center;
  }

  .footer-social-link1-title {
    text-align: center;
  }

  .footer-social-links {
    align-self: center;
  }

  .footer-row {
    flex-direction: column;
  }
}

@media(max-width: 479px) {
  .footer-actions {
    width: 100%;
  }

  .footer-form {
    width: 100%;
    flex-direction: column;
  }

  .footer-container {
    width: 100%;
  }

  .footer-button {
    width: 100%;
  }

  .footer-links {
    flex-direction: column;
  }

  .footer-column1 {
    width: 100%;
    max-width: 100%;
    align-items: center;
    justify-content: center;
  }

  .footer-footer-links {
    align-items: center;
    justify-content: center;
  }

  .footer-column2 {
    width: 100%;
    max-width: 100%;
    align-items: center;
    justify-content: center;
  }

  .footer-footer-links1 {
    align-items: center;
    justify-content: center;
  }

  .footer-column3 {
    width: 100%;
    max-width: 100%;
    align-items: center;
    justify-content: center;
  }

  .footer-social-links {
    align-items: center;
    justify-content: center;
  }

  .footer-credits {
    gap: 0;
  }

  .footer-row {
    align-items: center;
    justify-content: center;
  }
}

.home-container {
  width: 100%;
  display: flex;
  min-height: 50px;
  align-items: center;
  flex-direction: column;
}