<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Fix - FeedbackHub</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 500px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        h1 { color: #333; text-align: center; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 8px; font-weight: bold; }
        input { 
            width: 100%; 
            padding: 12px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            font-size: 16px; 
        }
        button { 
            width: 100%; 
            padding: 15px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            font-size: 16px; 
            cursor: pointer; 
            margin: 10px 0; 
        }
        button:hover { background: #0056b3; }
        .status { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .actions { display: flex; gap: 10px; margin-top: 20px; }
        .actions button { flex: 1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Admin Login Fix</h1>
        <div id="status"></div>
        
        <div class="form-group">
            <label for="email">Admin Email:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="password">Admin Password:</label>
            <input type="password" id="password" value="admin123">
        </div>
        
        <button onclick="loginAndTest()">🔑 Login & Test Admin Access</button>
        
        <div class="actions">
            <button onclick="clearStorage()">🗑️ Clear Storage</button>
            <button onclick="checkCurrentAuth()">🔍 Check Auth</button>
            <button onclick="goToAdmin()">🚀 Go to Admin</button>
        </div>
    </div>

    <script>
        async function loginAndTest() {
            const statusDiv = document.getElementById('status');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            statusDiv.innerHTML = '<div class="info">🔄 Logging in...</div>';
            
            try {
                // Step 1: Login
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });
                
                if (!loginResponse.ok) {
                    const error = await loginResponse.json();
                    throw new Error(error.error || 'Login failed');
                }
                
                const loginData = await loginResponse.json();
                localStorage.setItem('token', loginData.token);
                
                statusDiv.innerHTML = '<div class="success">✅ Login successful!</div>';
                
                // Step 2: Verify token
                setTimeout(async () => {
                    statusDiv.innerHTML += '<div class="info">🔍 Verifying token...</div>';
                    
                    const verifyResponse = await fetch('/api/auth/me', {
                        headers: { 'x-auth-token': loginData.token }
                    });
                    
                    if (!verifyResponse.ok) {
                        throw new Error('Token verification failed');
                    }
                    
                    const userData = await verifyResponse.json();
                    statusDiv.innerHTML += '<div class="success">✅ Token verified!</div>';
                    statusDiv.innerHTML += `<div class="info">👤 User: ${userData.user.email}</div>`;
                    statusDiv.innerHTML += `<div class="info">🎭 Role: ${userData.user.role}</div>`;
                    
                    // Step 3: Test admin access
                    setTimeout(async () => {
                        statusDiv.innerHTML += '<div class="info">🔍 Testing admin access...</div>';
                        
                        const adminResponse = await fetch('/api/admin/stats', {
                            headers: { 'x-auth-token': loginData.token }
                        });
                        
                        if (!adminResponse.ok) {
                            throw new Error(`Admin access failed: ${adminResponse.status}`);
                        }
                        
                        const adminData = await adminResponse.json();
                        statusDiv.innerHTML += '<div class="success">✅ Admin access confirmed!</div>';
                        statusDiv.innerHTML += `<div class="info">📊 Users: ${adminData.overview.totalUsers}</div>`;
                        statusDiv.innerHTML += `<div class="info">📦 Products: ${adminData.overview.totalProducts}</div>`;
                        statusDiv.innerHTML += '<div class="success">🎉 Ready to access admin panel!</div>';
                        
                    }, 1000);
                }, 1000);
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function checkCurrentAuth() {
            const statusDiv = document.getElementById('status');
            const token = localStorage.getItem('token');
            
            if (!token) {
                statusDiv.innerHTML = '<div class="warning">⚠️ No token found in localStorage</div>';
                return;
            }
            
            try {
                const response = await fetch('/api/auth/me', {
                    headers: { 'x-auth-token': token }
                });
                
                if (!response.ok) {
                    statusDiv.innerHTML = '<div class="error">❌ Token is invalid or expired</div>';
                    return;
                }
                
                const userData = await response.json();
                statusDiv.innerHTML = `
                    <div class="success">✅ Currently authenticated</div>
                    <div class="info">👤 User: ${userData.user.email}</div>
                    <div class="info">🎭 Role: ${userData.user.role}</div>
                    <div class="info">🔒 Active: ${userData.user.isActive}</div>
                `;
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        function clearStorage() {
            localStorage.removeItem('token');
            sessionStorage.clear();
            document.getElementById('status').innerHTML = '<div class="info">🗑️ Storage cleared</div>';
        }
        
        function goToAdmin() {
            window.location.href = '/admin.html';
        }
        
        // Check auth on page load
        window.addEventListener('load', checkCurrentAuth);
    </script>
</body>
</html>
