/* Profile Menu Dropdown Styles */
.profile-menu-container {
  position: relative;
  display: inline-block;
}

.profile-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  border-radius: 50%;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.profile-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.profile-btn img {
  border-radius: 50%;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.profile-btn:hover img {
  border-color: #007bff;
}

.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  border: 1px solid #e1e5e9;
  min-width: 280px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.profile-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-header {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.dropdown-avatar {
  border-radius: 50%;
  border: 2px solid #007bff;
}

.dropdown-user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.dropdown-name {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.dropdown-email {
  color: #666;
  font-size: 0.85rem;
}

.dropdown-divider {
  height: 1px;
  background: #e1e5e9;
  margin: 0;
}

.dropdown-menu {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.dropdown-menu li {
  margin: 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.dropdown-item:hover {
  background: #f8f9fa;
  color: #007bff;
}

.dropdown-item.logout-item:hover {
  background: #fee;
  color: #dc3545;
}

/* Icons for dropdown items */
.dropdown-item i {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.dropdown-item i::before {
  font-size: 16px;
}

/* Simple icon styles using CSS */
.icon-dashboard::before { content: "📊"; }
.icon-profile::before { content: "👤"; }
.icon-settings::before { content: "⚙️"; }
.icon-notifications::before { content: "🔔"; }
.icon-logout::before { content: "🚪"; }

/* Profile Settings Modal */
.profile-settings-modal .modal-content {
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.profile-settings-tabs {
  display: flex;
  border-bottom: 1px solid #e1e5e9;
  margin-bottom: 1.5rem;
}

.profile-tab {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.profile-tab.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.profile-tab:hover {
  color: #007bff;
  background: #f8f9fa;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.profile-form-group {
  margin-bottom: 1.5rem;
}

.profile-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.profile-avatar-section {
  text-align: center;
  margin-bottom: 2rem;
}

.profile-avatar-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid #e1e5e9;
  margin: 0 auto 1rem;
  object-fit: cover;
}

.avatar-upload-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.avatar-upload-btn:hover {
  background: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-dropdown {
    min-width: 260px;
    right: -20px;
  }
  
  .profile-form-row {
    grid-template-columns: 1fr;
  }
  
  .profile-settings-modal .modal-content {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }
}

/* Animation for dropdown */
@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-dropdown.show {
  animation: dropdownSlide 0.3s ease;
}

/* Notification badge for profile icon */
.profile-btn::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background: #dc3545;
  border-radius: 50%;
  border: 2px solid white;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.profile-btn.has-notifications::after {
  opacity: 1;
}

/* Success/Error messages in forms */
.form-message {
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.form-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
