<style>

/* Base styles */
:root {
  --primary-color: #048149;
  --secondary-color: #006dcc;
  --text-color: #1e1e1e;
  --text-muted: #585858;
  --bg-white: #fff;
  --bg-light: #f5f5f5;
  --border-color: #e2e2e2;
  --shadow-sm: 0 2px 0 -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 5px 20px 4px rgba(0, 0, 0, 0.2);
  --font-primary: "Segoe UI", system-ui, sans-serif;
  --spacing-base: 8px;
  --radius-base: 8px;
}

/* Utility classes */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* Layout components */
.profile-layout {
  background: var(--bg-light);
  padding: 109px 0 194px;
  width: 100%;
}

.profile-container {
  max-width: 1279px;
  margin: 0 auto;
  padding: 24px 70px 123px;
  display: flex;
  gap: 20px;
}

/* Header */
.main-header {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100;
  background: var(--bg-white);
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 86px;
  max-width: 1440px;
  margin: 0 auto;
}

.logo {
  width: 184px;
  height: auto;
}

/* Navigation */
.main-nav {
  display: flex;
  gap: var(--spacing-base);
}

.nav-link {
  padding: 8px 14px;
  font-weight: 700;
  color: var(--text-color);
  text-decoration: none;
}

.nav-link:hover,
.nav-link:focus {
  text-decoration: underline;
}

/* Profile sections */
.profile-section {
  background: var(--bg-white);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-sm);
  padding: 20px;
  margin-bottom: 15px;
}

.section-title {
  font-size: 20px;
  color: var(--text-color);
  margin-bottom: 20px;
}

/* Profile header */
.profile-header {
  display: flex;
  gap: 24px;
  align-items: center;
}

.profile-avatar {
  width: 207px;
  height: 162px;
  object-fit: cover;
  border-radius: var(--radius-base);
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-color);
}

.profile-role {
  font-size: 20px;
  color: var(--text-color);
  margin: 4px 0;
}

.profile-location {
  font-size: 16px;
  color: var(--text-muted);
}

/* Responsive styles */
@media (max-width: 991px) {
  .profile-container {
    flex-direction: column;
    padding: 20px;
  }

  .header-content {
    padding: 20px;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
  }

  .profile-avatar {
    width: 150px;
    height: 150px;
  }
}

/* Footer */
.main-footer {
  background: var(--bg-white);
  padding: 24px 80px;
  text-align: center;
  font-size: 12px;
  color: var(--text-color);
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.footer-link {
  color: var(--text-color);
  text-decoration: none;
}

.footer-link:hover,
.footer-link:focus {
  text-decoration: underline;
}

</style><header class="main-header" role="banner"><div class="header-content"><img src="https://cdn.builder.io/api/v1/image/assets/TEMP/355c5b488f1f604487ee331f0e99e1b8c8ba1161d8e844ee133542df423c99bc?placeholderIfAbsent=true&apiKey=03580385e222404c8df236551c67226a" alt="Feedback Hub Logo" class="logo"><nav class="main-nav" role="navigation" aria-label="Main navigation"><a href="#" class="nav-link">Product</a><a href="#" class="nav-link">Reviews</a><a href="#" class="nav-link">Industry</a><a href="#" class="nav-link">Response</a><a href="#" class="nav-link">Support</a><a href="#" class="nav-link">About Us</a></nav><div class="profile-actions"><img src="https://cdn.builder.io/api/v1/image/assets/TEMP/d2aacbe2f39d8d54b29edbe787aad6f7a2c479d33ce379546068ae0ffba92ec1?placeholderIfAbsent=true&apiKey=03580385e222404c8df236551c67226a" alt="User profile" class="profile-icon"><button class="logout-btn">Log Out</button></div></div></header><main class="profile-layout" role="main"><div class="profile-container"><section class="profile-section" aria-labelledby="profile-title"><h1 id="profile-title" class="visually-hidden">User Profile</h1><div class="profile-header"><img src="https://cdn.builder.io/api/v1/image/assets/TEMP/3e56478c32e66fbb2a11e0d641640e4e63d98e4ea8c7433df963218f3fc38d0f?placeholderIfAbsent=true&apiKey=03580385e222404c8df236551c67226a" alt="Sahil Katariya's profile photo" class="profile-avatar"><div class="profile-info"><h2 class="profile-name">Sahil Katariya</h2><p class="profile-role">Customer</p><p class="profile-location">Gujarat,
India</p></div></div></section>< !-- Rest of sections following same pattern --></div></main><footer class="main-footer" role="contentinfo"><p>© 2024,
FeedBack Hub,
Inc. Various trademarks held by their respective owners.</p><div class="footer-links"><a href="#" class="footer-link">Legal</a><a href="#" class="footer-link">Privacy Information</a><a href="#" class="footer-link">Terms of Use</a><a href="#" class="footer-link">Trust</a><a href="#" class="footer-link">Help</a><a href="#" class="footer-link">Contact</a></div></footer>