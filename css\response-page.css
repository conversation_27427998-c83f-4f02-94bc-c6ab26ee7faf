/* Response Page Styles */
.response-page {
  min-height: 100vh;
  background-color: var(--dl-color-theme-secondary1);
  background-image: url('/images/Index/monaco-iphone-wallpaper-picjumbo-com.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: scroll;
  background-blend-mode: soft-light;
  padding-top: 80px;
}

/* Hero Section */
.response-hero {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--dl-radius-radius-cardradius);
  box-shadow: var(--shadow-lg);
  text-align: center;
}

.response-hero .hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.response-hero .hero-text {
  color: var(--dl-color-theme-neutral-dark);
  margin-bottom: var(--dl-space-space-oneandhalfunits);
}

.response-hero .hero-text1 {
  color: var(--dl-color-theme-neutral-dark);
  opacity: 0.8;
}

/* Main Layout */
.response-layout {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--dl-radius-radius-cardradius);
  box-shadow: var(--shadow-lg);
}

.response-container {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: var(--dl-space-space-twounits);
  height: 70vh;
  min-height: 600px;
}

/* Product Sidebar */
.product-sidebar {
  background: var(--dl-color-theme-neutral-light);
  border-radius: var(--dl-radius-radius-cardradius);
  border: 1px solid var(--dl-color-theme-secondary2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: var(--dl-space-space-oneandhalfunits);
  border-bottom: 1px solid var(--dl-color-theme-secondary2);
  background: var(--dl-color-theme-secondary1);
}

.sidebar-header h2 {
  color: var(--dl-color-theme-neutral-dark);
  margin-bottom: var(--dl-space-space-unit);
}

.search-container {
  position: relative;
}

.search-input-small {
  width: 100%;
  padding: var(--dl-space-space-halfunit) var(--dl-space-space-twounits) var(--dl-space-space-halfunit) var(--dl-space-space-unit);
  border: 1px solid var(--dl-color-theme-secondary2);
  border-radius: var(--dl-radius-radius-buttonradius);
  font-size: 0.9rem;
  background: var(--dl-color-theme-neutral-light);
}

.search-btn-small {
  position: absolute;
  right: var(--dl-space-space-halfunit);
  top: 50%;
  transform: translateY(-50%);
  background: var(--dl-color-theme-primary1);
  border: none;
  border-radius: var(--dl-radius-radius-buttonradius);
  padding: var(--dl-space-space-halfunit);
  cursor: pointer;
}

.search-icon-small {
  width: 16px;
  height: 16px;
  fill: var(--dl-color-theme-neutral-light);
}

.products-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--dl-space-space-unit);
}

.product-item {
  display: flex;
  align-items: center;
  padding: var(--dl-space-space-unit);
  margin-bottom: var(--dl-space-space-halfunit);
  border-radius: var(--dl-radius-radius-cardradius);
  cursor: pointer;
  transition: all var(--transition-base);
  border: 1px solid transparent;
}

.product-item:hover {
  background: var(--dl-color-theme-secondary2);
  border-color: var(--dl-color-theme-primary1);
  transform: translateX(4px);
}

.product-item.active {
  background: var(--dl-color-theme-primary1);
  color: var(--dl-color-theme-neutral-light);
}

.product-item-img {
  width: 40px;
  height: 40px;
  border-radius: var(--dl-radius-radius-imageradius);
  margin-right: var(--dl-space-space-unit);
  object-fit: cover;
}

.product-item-info h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: var(--dl-space-space-halfunit);
  line-height: 1.2;
}

.product-item-info p {
  font-size: 0.8rem;
  opacity: 0.8;
  margin: 0;
}

/* Chat Interface */
.chat-interface {
  display: flex;
  flex-direction: column;
  background: var(--dl-color-theme-neutral-light);
  border-radius: var(--dl-radius-radius-cardradius);
  border: 1px solid var(--dl-color-theme-secondary2);
  overflow: hidden;
}

.chat-header {
  padding: var(--dl-space-space-oneandhalfunits);
  border-bottom: 1px solid var(--dl-color-theme-secondary2);
  background: var(--dl-color-theme-secondary1);
}

.chat-product-info {
  display: flex;
  align-items: center;
}

.chat-product-img {
  width: 50px;
  height: 50px;
  border-radius: var(--dl-radius-radius-imageradius);
  margin-right: var(--dl-space-space-unit);
  object-fit: cover;
}

.chat-product-details h3 {
  color: var(--dl-color-theme-neutral-dark);
  font-size: 1.1rem;
  margin-bottom: var(--dl-space-space-halfunit);
}

.chat-product-details p {
  color: var(--dl-color-theme-neutral-dark);
  opacity: 0.7;
  font-size: 0.9rem;
  margin: 0;
}

.chat-welcome {
  text-align: center;
  color: var(--dl-color-theme-neutral-dark);
}

.welcome-icon {
  font-size: 3rem;
  margin-bottom: var(--dl-space-space-unit);
}

.chat-welcome h3 {
  margin-bottom: var(--dl-space-space-halfunit);
}

.chat-welcome p {
  opacity: 0.7;
  margin: 0;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--dl-space-space-unit);
  display: flex;
  flex-direction: column;
  gap: var(--dl-space-space-unit);
}

.message {
  display: flex;
  align-items: flex-start;
  gap: var(--dl-space-space-unit);
  animation: slideInUp 0.3s ease;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: var(--dl-color-theme-primary1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dl-color-theme-neutral-light);
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.message.user .message-avatar {
  background: var(--dl-color-theme-accent1);
}

.message-content {
  max-width: 70%;
  background: var(--dl-color-theme-secondary2);
  padding: var(--dl-space-space-unit) var(--dl-space-space-oneandhalfunits);
  border-radius: var(--dl-radius-radius-cardradius);
  position: relative;
}

.message.user .message-content {
  background: var(--dl-color-theme-primary1);
  color: var(--dl-color-theme-neutral-light);
}

.message-text {
  margin: 0;
  line-height: 1.5;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.6;
  margin-top: var(--dl-space-space-halfunit);
}

.message-actions {
  display: flex;
  gap: var(--dl-space-space-halfunit);
  margin-top: var(--dl-space-space-halfunit);
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  padding: var(--dl-space-space-halfunit);
  border-radius: var(--dl-radius-radius-buttonradius);
  cursor: pointer;
  font-size: 0.8rem;
  transition: background var(--transition-base);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Chat Input */
.chat-input-container {
  padding: var(--dl-space-space-unit);
  border-top: 1px solid var(--dl-color-theme-secondary2);
  background: var(--dl-color-theme-secondary1);
}

.chat-input-wrapper {
  display: flex;
  gap: var(--dl-space-space-unit);
  margin-bottom: var(--dl-space-space-unit);
}

.chat-input {
  flex: 1;
  padding: var(--dl-space-space-unit);
  border: 1px solid var(--dl-color-theme-secondary2);
  border-radius: var(--dl-radius-radius-buttonradius);
  background: var(--dl-color-theme-neutral-light);
  resize: none;
}

.send-btn {
  background: var(--dl-color-theme-primary1);
  border: none;
  padding: var(--dl-space-space-unit);
  border-radius: var(--dl-radius-radius-buttonradius);
  cursor: pointer;
  transition: background var(--transition-base);
}

.send-btn:hover {
  background: var(--dl-color-theme-accent1);
}

.send-icon {
  width: 20px;
  height: 20px;
  fill: var(--dl-color-theme-neutral-light);
}

.quick-questions {
  display: flex;
  gap: var(--dl-space-space-halfunit);
  flex-wrap: wrap;
}

.quick-question-btn {
  background: var(--dl-color-theme-neutral-light);
  border: 1px solid var(--dl-color-theme-secondary2);
  padding: var(--dl-space-space-halfunit) var(--dl-space-space-unit);
  border-radius: var(--dl-radius-radius-buttonradius);
  cursor: pointer;
  font-size: 0.8rem;
  transition: all var(--transition-base);
}

.quick-question-btn:hover {
  background: var(--dl-color-theme-primary1);
  color: var(--dl-color-theme-neutral-light);
  border-color: var(--dl-color-theme-primary1);
}

/* Anonymous Chat Panel */
.anonymous-chat-panel {
  background: var(--dl-color-theme-neutral-light);
  border-radius: var(--dl-radius-radius-cardradius);
  border: 1px solid var(--dl-color-theme-secondary2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-panel-header {
  padding: var(--dl-space-space-unit);
  border-bottom: 1px solid var(--dl-color-theme-secondary2);
  background: var(--dl-color-theme-secondary1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-tabs {
  display: flex;
  gap: var(--dl-space-space-halfunit);
}

.chat-tab {
  background: var(--dl-color-theme-neutral-light);
  border: 1px solid var(--dl-color-theme-secondary2);
  padding: var(--dl-space-space-halfunit) var(--dl-space-space-unit);
  border-radius: var(--dl-radius-radius-buttonradius);
  cursor: pointer;
  font-size: 0.8rem;
  transition: all var(--transition-base);
  color: var(--dl-color-theme-neutral-dark);
}

.chat-tab.active {
  background: var(--dl-color-theme-primary1);
  color: var(--dl-color-theme-neutral-light);
  border-color: var(--dl-color-theme-primary1);
}

.chat-tab:hover:not(.active) {
  background: var(--dl-color-theme-secondary2);
}

.close-panel {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--dl-color-theme-neutral-dark);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background var(--transition-base);
}

.close-panel:hover {
  background: var(--dl-color-theme-secondary2);
}

.tab-content {
  flex: 1;
  padding: var(--dl-space-space-unit);
  overflow-y: auto;
  display: none;
}

.tab-content.active {
  display: flex;
  flex-direction: column;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: var(--dl-space-space-unit);
  margin-bottom: var(--dl-space-space-twounits);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--dl-space-space-halfunit);
  background: var(--dl-color-theme-secondary2);
  border-radius: var(--dl-radius-radius-cardradius);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--dl-color-theme-neutral-dark);
}

.stat-value {
  font-weight: 600;
  color: var(--dl-color-theme-primary1);
}

.summary-insights {
  background: var(--dl-color-theme-secondary2);
  padding: var(--dl-space-space-unit);
  border-radius: var(--dl-radius-radius-cardradius);
}

/* Community Chat Styles */
.community-chat-header {
  margin-bottom: var(--dl-space-space-unit);
  padding-bottom: var(--dl-space-space-unit);
  border-bottom: 1px solid var(--dl-color-theme-secondary2);
}

.community-chat-header h4 {
  color: var(--dl-color-theme-neutral-dark);
  margin-bottom: var(--dl-space-space-halfunit);
  font-size: 1rem;
}

.user-status {
  display: flex;
  align-items: center;
  gap: var(--dl-space-space-halfunit);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--dl-color-theme-secondary2);
}

.status-indicator.online {
  background: #4CAF50;
  animation: pulse 2s infinite;
}

.status-indicator.away {
  background: #FF9800;
}

.status-indicator.offline {
  background: #9E9E9E;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

#anonymous-username {
  font-size: 0.8rem;
  color: var(--dl-color-theme-neutral-dark);
  font-weight: 500;
}

.change-username-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  padding: var(--dl-space-space-halfunit);
  border-radius: var(--dl-radius-radius-buttonradius);
  transition: background var(--transition-base);
}

.change-username-btn:hover {
  background: var(--dl-color-theme-secondary2);
}

.community-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: var(--dl-space-space-unit);
  max-height: 300px;
  padding: var(--dl-space-space-halfunit);
  background: var(--dl-color-theme-secondary1);
  border-radius: var(--dl-radius-radius-cardradius);
}

.community-message {
  display: flex;
  align-items: flex-start;
  gap: var(--dl-space-space-halfunit);
  margin-bottom: var(--dl-space-space-unit);
  animation: slideInUp 0.3s ease;
}

.community-message.own {
  flex-direction: row-reverse;
}

.community-message.ai {
  background: rgba(var(--dl-color-theme-primary1-rgb), 0.1);
  padding: var(--dl-space-space-halfunit);
  border-radius: var(--dl-radius-radius-cardradius);
  border-left: 3px solid var(--dl-color-theme-primary1);
}

.community-avatar {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: var(--dl-color-theme-accent1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dl-color-theme-neutral-light);
  font-weight: 600;
  font-size: 0.7rem;
  flex-shrink: 0;
}

.community-avatar.ai {
  background: var(--dl-color-theme-primary1);
}

.community-avatar.own {
  background: var(--dl-color-theme-secondary2);
  color: var(--dl-color-theme-neutral-dark);
}

.community-message-content {
  max-width: 80%;
  background: var(--dl-color-theme-neutral-light);
  padding: var(--dl-space-space-halfunit) var(--dl-space-space-unit);
  border-radius: var(--dl-radius-radius-cardradius);
  border: 1px solid var(--dl-color-theme-secondary2);
}

.community-message.own .community-message-content {
  background: var(--dl-color-theme-accent1);
  color: var(--dl-color-theme-primary1);
}

.community-message.ai .community-message-content {
  background: var(--dl-color-theme-primary1);
  color: var(--dl-color-theme-neutral-light);
  border-color: var(--dl-color-theme-primary1);
}

.community-message-text {
  margin: 0;
  font-size: 0.8rem;
  line-height: 1.4;
}

.community-message-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--dl-space-space-halfunit);
  font-size: 0.7rem;
  opacity: 0.7;
}

.community-username {
  font-weight: 500;
}

.community-time {
  font-size: 0.6rem;
}

.community-input-container {
  border-top: 1px solid var(--dl-color-theme-secondary2);
  padding-top: var(--dl-space-space-unit);
}

.community-input-wrapper {
  display: flex;
  gap: var(--dl-space-space-halfunit);
  margin-bottom: var(--dl-space-space-unit);
}

.community-input {
  flex: 1;
  padding: var(--dl-space-space-halfunit) var(--dl-space-space-unit);
  border: 1px solid var(--dl-color-theme-secondary2);
  border-radius: var(--dl-radius-radius-buttonradius);
  background: var(--dl-color-theme-neutral-light);
  font-size: 0.8rem;
}

.community-send-btn {
  background: var(--dl-color-theme-primary1);
  border: none;
  padding: var(--dl-space-space-halfunit);
  border-radius: var(--dl-radius-radius-buttonradius);
  cursor: pointer;
  transition: background var(--transition-base);
}

.community-send-btn:hover {
  background: var(--dl-color-theme-accent1);
}

.chat-options {
  display: flex;
  gap: var(--dl-space-space-halfunit);
  flex-wrap: wrap;
}

.option-btn {
  background: var(--dl-color-theme-secondary2);
  border: 1px solid var(--dl-color-theme-secondary2);
  padding: var(--dl-space-space-halfunit);
  border-radius: var(--dl-radius-radius-buttonradius);
  cursor: pointer;
  font-size: 0.7rem;
  transition: all var(--transition-base);
  color: var(--dl-color-theme-neutral-dark);
}

.option-btn:hover {
  background: var(--dl-color-theme-primary1);
  color: var(--dl-color-theme-neutral-light);
  border-color: var(--dl-color-theme-primary1);
}

/* Floating Action Buttons */
.floating-actions {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: flex;
  flex-direction: column;
  gap: var(--dl-space-space-unit);
  z-index: 1000;
}

.fab {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--dl-color-theme-primary1);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);
}

.fab:hover {
  background: var(--dl-color-theme-accent1);
  transform: scale(1.1);
}

.fab svg {
  width: 24px;
  height: 24px;
  fill: var(--dl-color-theme-neutral-light);
}

.community-fab {
  background: var(--dl-color-theme-accent1);
  animation: bounce 2s infinite;
}

.community-fab:hover {
  background: var(--dl-color-theme-primary1);
  animation: none;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Loading States */
.loading-spinner-small {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--dl-color-theme-secondary2);
  border-top: 2px solid var(--dl-color-theme-primary1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--dl-space-space-halfunit);
}

.loading-products {
  text-align: center;
  padding: var(--dl-space-space-twounits);
  color: var(--dl-color-theme-neutral-dark);
  animation: fadeIn 0.3s ease-in;
}

.no-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

.products-list.loaded {
  animation: slideIn 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--dl-space-space-halfunit);
  padding: var(--dl-space-space-unit);
  background: var(--dl-color-theme-secondary2);
  border-radius: var(--dl-radius-radius-cardradius);
  margin: var(--dl-space-space-unit) 0;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--dl-color-theme-primary1);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Active Navigation Link */
.nav-link.active {
  color: var(--dl-color-theme-primary1) !important;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .response-container {
    grid-template-columns: 250px 1fr;
  }

  .anonymous-chat-panel {
    display: none;
  }
}

@media (max-width: 768px) {
  .response-page {
    padding-top: 60px;
  }

  .response-container {
    grid-template-columns: 1fr;
    height: auto;
    min-height: 500px;
  }

  .product-sidebar {
    order: 2;
    max-height: 200px;
  }

  .chat-interface {
    order: 1;
    min-height: 400px;
  }

  .message-content {
    max-width: 85%;
  }

  .floating-actions {
    bottom: 20px;
    right: 20px;
  }

  .fab {
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .quick-questions {
    flex-direction: column;
  }

  .chat-input-wrapper {
    flex-direction: column;
  }

  .message-content {
    max-width: 90%;
  }
}
