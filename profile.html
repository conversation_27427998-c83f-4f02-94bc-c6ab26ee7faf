<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile</title>
    <link rel="stylesheet" href="profile.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="index.css">
    <link rel="stylesheet" href="css/profile-menu.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/profile-enhanced.css">
    <link rel="icon" href="ico.ico" type="image/x-icon" />

</head>

<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header class="main-header" role="banner">
        <div class="header-content">
            <a href="/index.html" class="logo-link">
                <img src="icon_trans.png" alt="Feedback Hub" width="184" height="80" class="logo">
            </a>
            <div data-thq="navbar-nav" class="navbar-desktop-menu">
                <nav class="navbar-links" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li><a href="products.html" class="nav-link">Products</a></li>
                        <li><a href="reviews.html" class="nav-link">Reviews</a></li>
                        <li><a href="index.html#community-content" class="nav-link">Industry</a></li>
                        <li><a href="response.html" class="nav-link">Response</a></li>
                        <li><a href="index.html#contact-contant" class="nav-link">Support</a></li>
                        <li><a href="index.html#about-us" class="nav-link">About Us</a></li>
                    </ul>
                </nav>
                <div class="user-actions" role="navigation" aria-label="User menu">
                    <div class="profile-menu-container">
                        <button class="profile-btn" aria-label="Profile Menu" id="profile-menu-btn">
                            <img src="profile_sml.png" alt="" width="43" height="43" id="profile-avatar">
                            <span class="visually-hidden">Profile Menu</span>
                        </button>
                        <div class="profile-dropdown" id="profile-dropdown">
                            <div class="dropdown-header">
                                <img src="profile_sml.png" alt="" width="40" height="40" class="dropdown-avatar">
                                <div class="dropdown-user-info">
                                    <span class="dropdown-name" id="dropdown-user-name">Loading...</span>
                                    <span class="dropdown-email" id="dropdown-user-email">Loading...</span>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <ul class="dropdown-menu">
                                <li><a href="/profile.html" class="dropdown-item"><i class="icon-profile"></i>Profile</a></li>
                                <li><a href="#" class="dropdown-item" id="settings-btn"><i class="icon-settings"></i>Settings</a></li>
                                <li><a href="#" class="dropdown-item" id="view-notifications-btn"><i class="icon-notifications"></i>Notifications</a></li>
                                <li class="dropdown-divider"></li>
                                <li><a href="#" class="dropdown-item logout-item" id="logout-btn"><i class="icon-logout"></i>Log Out</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

    </header>
    <main id="main-content" class="profile-layout" role="main">
        <div class="profile-container">
            <div class="profile-main">
                <section class="profile-header" aria-labelledby="profile-title">
                    <div class="profile-info">
                        <div class="avatar-container">
                            <img src="profile photo.svg" alt="" class="profile-avatar" width="207" height="162" id="main-profile-avatar">
                            <div class="online-status" id="online-status">
                                <span class="status-indicator online"></span>
                                <span class="status-text">Online</span>
                            </div>
                        </div>
                        <div class="profile-details">
                            <h1 id="profile-title" class="profile-name">Loading...</h1>
                            <p class="profile-role">Customer</p>
                            <p class="profile-location">
                                <i class="icon-location"></i>
                                <span id="location-text">Loading...</span>
                            </p>
                            <p class="profile-bio" id="profile-bio">
                                <i class="icon-info"></i>
                                <span id="bio-text">No bio available</span>
                            </p>
                            <div class="profile-stats-mini">
                                <span class="mini-stat">
                                    <strong id="mini-reviews">0</strong> Reviews
                                </span>
                                <span class="mini-stat">
                                    <strong id="mini-rating">0.0</strong> Avg Rating
                                </span>
                                <span class="mini-stat">
                                    <strong id="mini-joined">Member since</strong> <span id="join-date">2024</span>
                                </span>
                            </div>
                        </div>
                        <div class="profile-actions">
                            <button class="edit-profile-btn" aria-label="Edit profile">
                                <img src="edit.svg" alt="edit-btn">
                            </button>
                            <button class="share-profile-btn" id="share-profile-btn">
                                <i class="icon-share"></i>
                                <span>Share</span>
                            </button>
                        </div>
                    </div>
                </section>
                
                <section class="dashboard-stats-section" aria-labelledby="stats-title">
                    <div class="section-header">
                        <h2 id="stats-title">Dashboard Overview</h2>
                        <div class="section-controls">
                            <button class="refresh-btn" id="refresh-stats-btn" title="Refresh Statistics">
                                <i class="icon-refresh"></i>
                            </button>
                            <select class="time-filter" id="stats-time-filter">
                                <option value="all">All Time</option>
                                <option value="month">This Month</option>
                                <option value="week">This Week</option>
                            </select>
                        </div>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card clickable" data-stat="reviews">
                            <div class="stat-icon">
                                <i class="icon-reviews"></i>
                            </div>
                            <div class="stat-content">
                                <h3>Total Reviews</h3>
                                <p class="stat-value" id="total-reviews">
                                    <span class="number">0</span>
                                    <span class="trend" id="reviews-trend"></span>
                                </p>
                                <small class="stat-subtitle">Your contributions</small>
                            </div>
                        </div>
                        <div class="stat-card clickable" data-stat="rating">
                            <div class="stat-icon">
                                <i class="icon-star"></i>
                            </div>
                            <div class="stat-content">
                                <h3>Average Rating</h3>
                                <p class="stat-value" id="avg-rating">
                                    <span class="number">0.0</span>
                                    <span class="stars" id="rating-stars"></span>
                                </p>
                                <small class="stat-subtitle">Community average</small>
                            </div>
                        </div>
                        <div class="stat-card clickable" data-stat="products">
                            <div class="stat-icon">
                                <i class="icon-products"></i>
                            </div>
                            <div class="stat-content">
                                <h3>Products</h3>
                                <p class="stat-value" id="total-products">
                                    <span class="number">0</span>
                                    <span class="trend" id="products-trend"></span>
                                </p>
                                <small class="stat-subtitle">Available to review</small>
                            </div>
                        </div>
                        <div class="stat-card clickable" data-stat="engagement">
                            <div class="stat-icon">
                                <i class="icon-engagement"></i>
                            </div>
                            <div class="stat-content">
                                <h3>Engagement</h3>
                                <p class="stat-value" id="engagement-score">
                                    <span class="number">0</span>
                                    <span class="percentage">%</span>
                                </p>
                                <small class="stat-subtitle">Activity level</small>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="products-section" aria-labelledby="products-title">
                    <div class="section-header">
                        <h2 id="products-title">Available Products</h2>
                        <div class="section-controls">
                            <div class="search-container">
                                <input type="text" id="product-search" placeholder="Search products..." class="search-input">
                                <i class="icon-search search-icon"></i>
                            </div>
                            <select class="filter-select" id="product-category-filter">
                                <option value="all">All Categories</option>
                                <option value="electronics">Electronics</option>
                                <option value="software">Software</option>
                                <option value="books">Books</option>
                                <option value="home">Home & Garden</option>
                            </select>
                            <select class="sort-select" id="product-sort">
                                <option value="newest">Newest First</option>
                                <option value="oldest">Oldest First</option>
                                <option value="name">Name A-Z</option>
                                <option value="price-low">Price: Low to High</option>
                                <option value="price-high">Price: High to Low</option>
                            </select>
                        </div>
                    </div>
                    <div class="products-stats">
                        <span id="products-count">0 products found</span>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid" title="Grid View">
                                <i class="icon-grid"></i>
                            </button>
                            <button class="view-btn" data-view="list" title="List View">
                                <i class="icon-list"></i>
                            </button>
                        </div>
                    </div>
                    <div id="products-container" class="products-grid">
                        <!-- Products will be loaded here -->
                    </div>
                    <div class="products-pagination" id="products-pagination">
                        <!-- Pagination will be added here -->
                    </div>
                </section>

                <section class="reviews-section" aria-labelledby="reviews-title">
                    <div class="section-header">
                        <h2 id="reviews-title">My Reviews</h2>
                        <div class="section-controls">
                            <div class="review-tabs">
                                <button class="tab-btn active" data-tab="my-reviews">My Reviews</button>
                                <button class="tab-btn" data-tab="all-reviews">All Reviews</button>
                                <button class="tab-btn" data-tab="recent">Recent</button>
                            </div>
                            <select class="filter-select" id="review-rating-filter">
                                <option value="all">All Ratings</option>
                                <option value="5">5 Stars</option>
                                <option value="4">4 Stars</option>
                                <option value="3">3 Stars</option>
                                <option value="2">2 Stars</option>
                                <option value="1">1 Star</option>
                            </select>
                        </div>
                    </div>
                    <div class="reviews-stats">
                        <span id="reviews-count">0 reviews found</span>
                        <button class="add-review-btn" id="quick-add-review">
                            <i class="icon-plus"></i>
                            Add Review
                        </button>
                    </div>
                    <div id="reviews-container" class="reviews-list">
                        <!-- Reviews will be loaded here -->
                    </div>
                    <div class="reviews-pagination" id="reviews-pagination">
                        <!-- Pagination will be added here -->
                    </div>
                </section>
                <!-- <section class="badges-section" aria-labelledby="badges-title">
                    <div class="badges-header">
                        <h2 id="badges-title">4 Badges</h2>
                        <div class="badges-filter">
                            <button class="filter-btn" aria-expanded="true" aria-controls="filter-menu">
                                Filter by All Badges
                            </button>
                            <label for="menu" class="filter-btn" aria-controls="filter-menu"></label>
                            <img src="edit.svg" alt="edit-btn" >
                            <select id="menu" name="menu"
                                style="border: 1px solid #ccc; padding: 5px; border-radius: 5px;">
                                <option value="option1">Filter All Badges</option>
                                <option value="option2">Wonderfull</option>
                                <option value="option3">Excellent</option>
                                <option value="option4">Great</option>
                            </select>
                        </div>
                    </div>
                    <ul class="badges-grid" role="list">
                        <li class="badge-item">
                            <img src="badge.png" alt="" width="100" height="100">
                            <p>Quick Start: Build Your First Agent with Agentforce</p>
                        </li>
                        <li class="badge-item">
                            <img src="badge.png" alt="" width="100" height="100">
                            <p>Quick Start: Build Your First Agent with Agentforce</p>
                        </li>
                        <li class="badge-item">
                            <img src="badge.png" alt="" width="100" height="100">
                            <p>Quick Start: Build Your First Agent with Agentforce</p>
                        </li>
                    </ul>
                </section> -->
            </div>

            <aside class="profile-sidebar">
                <section class="track-rank" aria-labelledby="track-title">
                    <h2 id="track-title">Feedback Track</h2>
                    <div class="progress-container">
                        <div class="level-indicator">
                            <span class="current-level" id="current-level">Level 1</span>
                            <span class="next-level" id="next-level">Level 2</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text" id="progress-text">0 / 100 XP</span>
                    </div>
                    <img src="level.png" alt="Track progress visualization" width="177" height="132">
                    <div class="track-stats">
                        <div class="stat-item clickable" data-tooltip="Total badges earned">
                            <p class="stat-value" id="user-badges">1</p>
                            <p class="stat-label">Badges</p>
                            <div class="stat-trend" id="badges-trend">+0</div>
                        </div>
                        <div class="stat-item clickable" data-tooltip="Your feedback score">
                            <p class="stat-value" id="user-score">500</p>
                            <p class="stat-label">Score</p>
                            <div class="stat-trend" id="score-trend">+0</div>
                        </div>
                    </div>
                </section>

                <section class="connections" aria-labelledby="connections-title">
                    <h2 id="connections-title">Connections</h2>
                    <div class="connection-stats">
                        <div class="stat-item clickable" data-action="view-followers">
                            <p class="stat-value" id="user-followers">0</p>
                            <p class="stat-label">Followers</p>
                            <div class="stat-change" id="followers-change">+0</div>
                        </div>
                        <div class="stat-item clickable" data-action="view-following">
                            <p class="stat-value" id="user-following">0</p>
                            <p class="stat-label">Following</p>
                            <div class="stat-change" id="following-change">+0</div>
                        </div>
                        <div class="stat-item clickable" data-action="view-reviews">
                            <p class="stat-value" id="user-reviews-count">1</p>
                            <p class="stat-label">Reviews</p>
                            <div class="stat-change" id="reviews-change">+0</div>
                        </div>
                    </div>
                    <div class="recent-activity" id="recent-activity">
                        <h4>Recent Activity</h4>
                        <div class="activity-list" id="activity-list">
                            <!-- Recent activities will be loaded here -->
                        </div>
                    </div>
                </section>

                <section class="quick-actions" aria-labelledby="actions-title">
                    <h2 id="actions-title">Quick Actions</h2>
                    <div class="action-buttons">
                        <button class="action-btn primary" id="add-product-btn">
                            <i class="icon-plus"></i>
                            <span>Add Product</span>
                            <div class="btn-tooltip">Add a new product for review</div>
                        </button>
                        <button class="action-btn" id="view-notifications-btn">
                            <i class="icon-notifications"></i>
                            <span>Notifications</span>
                            <div class="notification-badge" id="notification-badge">0</div>
                            <div class="btn-tooltip">View your notifications</div>
                        </button>
                        <button class="action-btn" id="settings-btn">
                            <i class="icon-settings"></i>
                            <span>Settings</span>
                            <div class="btn-tooltip">Manage your account</div>
                        </button>
                        <button class="action-btn" id="export-data-btn">
                            <i class="icon-download"></i>
                            <span>Export Data</span>
                            <div class="btn-tooltip">Download your data</div>
                        </button>
                    </div>
                </section>

                <!-- Real-time Updates Section -->
                <section class="live-updates" aria-labelledby="updates-title">
                    <h2 id="updates-title">Live Updates</h2>
                    <div class="updates-container" id="updates-container">
                        <div class="update-item">
                            <i class="icon-info"></i>
                            <span>Welcome to your profile!</span>
                        </div>
                    </div>
                    <div class="auto-refresh">
                        <label class="toggle-switch">
                            <input type="checkbox" id="auto-refresh-toggle" checked>
                            <span class="slider"></span>
                        </label>
                        <span>Auto-refresh</span>
                    </div>
                </section>
            </aside>
        </div>
    </main>
    <footer class="footer2-container" role="contentinfo">
        <div class="footer2-content">
            <p class="copyright-text">© 2024, FeedBack Hub, Inc. Various trademarks held by their respective owners.
                FeedBack, Inc. Feedback Tower, 416 Mission Street, 8rd Floor, San Francisco, United States</p>

            <div class="footer2-links-container">
                <nav class="footer2-nav" aria-label="footer2 navigation">
                    <a href="#" tabindex="0">Legal</a>
                    <a href="#" tabindex="0">Privacy Information</a>
                    <a href="#" tabindex="0">Responsible Disclosure</a>
                    <a href="#" tabindex="0">Terms of Use</a>
                    <a href="#" tabindex="0">Trust</a>
                    <a href="#" tabindex="0">Help</a>
                    <a href="#" tabindex="0">Contact</a>
                    <button type="button" tabindex="0">Cookie Preferences</button>
                </nav>

                <div class="privacy-choices" role="complementary">
                    <img loading="lazy" src="/images/Index/right.png" alt="" class="privacy-icon" />
                    <span class="privacy-text">Your Privacy Choices</span>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/api.js"></script>
    <script src="js/profile-menu.js"></script>
    <script src="js/profile.js"></script>
</body>

</html>