/* Dashboard Styles */
.dashboard-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.user-welcome h3 {
  margin: 0;
  color: #333;
}

.user-welcome p {
  margin: 0.5rem 0 0;
  color: #666;
}

.dashboard-stats {
  display: flex;
  gap: 1rem;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 1rem;
  min-width: 120px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.stat-card p {
  margin: 0.5rem 0 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.dashboard-section {
  background: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.dashboard-section h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.product-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.product-card img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.product-card h3 {
  margin: 1rem;
  font-size: 1.2rem;
}

.product-card p {
  margin: 0 1rem 1rem;
  color: #666;
}

.product-card .price {
  font-weight: bold;
  color: #333;
}

.product-card .btn {
  margin: 0 1rem 1rem;
}

.reviews-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.review-card {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.review-card h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.review-card .rating {
  color: #f8c51c;
  margin-bottom: 0.5rem;
}

.review-card p {
  margin: 0.5rem 0;
}

.review-card small {
  color: #999;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #fff;
  border-radius: 8px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  position: relative;
}

.close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  cursor: pointer;
}

#review-form .form-group {
  margin-bottom: 1rem;
}

#review-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

#review-form select,
#review-form textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

#review-form textarea {
  min-height: 100px;
}

/* Responsive */
@media (min-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 767px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .dashboard-stats {
    margin-top: 1rem;
    width: 100%;
    justify-content: space-between;
  }
  
  .stat-card {
    flex: 1;
  }
}