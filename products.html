<!DOCTYPE html>
<html lang="en">

<head>
  <title>Products - FeedBack Hub</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta charset="utf-8" />
  <meta property="twitter:card" content="summary_large_image" />
  <link rel="icon" href="ico.ico" type="image/x-icon" />

  <link rel="stylesheet" href="https://unpkg.com/animate.css@4.1.1/animate.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet" href="https://unpkg.com/@teleporthq/teleport-custom-scripts/dist/style.css" />
  <link rel="stylesheet" href="./index.css">
  <link rel="stylesheet" href="./style.css">
  <link rel="stylesheet" href="./profile.css">
  <link rel="stylesheet" href="./css/profile-menu.css">
  <link rel="stylesheet" href="./css/products-catalog.css">
</head>

<body>
  <a href="#main-content" class="skip-link">Skip to main content</a>
  <div class="home-container">
    <header class="main-header" role="banner">
        <div class="header-content">
          <a href="/index.html" class="logo-link">
            <img src="icon_trans.png" alt="Feedback Hub" width="184" height="80" class="logo">
          </a>
          <div data-thq="navbar-nav" class="navbar-desktop-menu">
            <nav class="navbar-links" role="navigation" aria-label="Main navigation">
              <ul class="nav-list">
                <li><a href="products.html" class="nav-link">Products</a></li>
                <li><a href="reviews.html" class="nav-link">Reviews</a></li>
                <li><a href="index.html#community-content" class="nav-link">Industry</a></li>
                <li><a href="response.html" class="nav-link">Response</a></li>
                <li><a href="index.html#contact-contant" class="nav-link">Support</a></li>
                <li><a href="index.html#about-us" class="nav-link">About Us</a></li>
              </ul>
            </nav>
            <div class="user-actions" role="navigation" aria-label="User menu" id="guest-actions">
              <a href="/login.html" button class="button-outline">
                <span class="body-small">Log in</span>
              </a>
              <a href="/signup.html" button class="button-filled">
                <span class="body-small">sign up</span>
              </a>
            </div>
            <div class="user-actions" role="navigation" aria-label="User menu" id="user-actions" style="display: none;">
              <div class="profile-menu-container">
                <button class="profile-btn" aria-label="Profile Menu" id="profile-menu-btn">
                  <img src="profile_sml.png" alt="profile" width="43" height="43" id="profile-avatar">
                  <span class="visually-hidden">Profile Menu</span>
                </button>
                <div class="profile-dropdown" id="profile-dropdown">
                  <a href="/profile.html" div class="dropdown-header">
                    <img src="profile_sml.png" alt="" width="40" height="40" class="dropdown-avatar">
                    <div class="dropdown-user-info">
                      <span class="dropdown-name" id="dropdown-user-name">Loading...</span>
                      <span class="dropdown-email" id="dropdown-user-email">Loading...</span>
                    </div>
                  </a>
                  <div class="dropdown-divider"></div>
                  <ul class="dropdown-menu">
                    <li><a href="/profile.html" class="dropdown-item"><i class="icon-profile"></i>Profile</a></li>
                    <li><a href="#" class="dropdown-item" id="settings-btn"><i class="icon-settings"></i>Settings</a></li>
                    <li><a href="#" class="dropdown-item" id="notifications-btn"><i class="icon-notifications"></i>Notifications</a></li>
                    <li class="dropdown-divider"></li>
                    <li><a href="#" class="dropdown-item logout-item" id="logout-btn"><i class="icon-logout"></i>Log Out</a></li>
                  </ul>
                </div>
              </div>
            </div>

            <div data-thq="burger-menu" class="navbar-burger-menu">
              <svg viewBox="0 0 1024 1024" class="navbar-icon">
                <path
                  d="M128 554.667h768c23.552 0 42.667-19.115 42.667-42.667s-19.115-42.667-42.667-42.667h-768c-23.552 0-42.667 19.115-42.667 42.667s19.115 42.667 42.667 42.667zM128 298.667h768c23.552 0 42.667-19.115 42.667-42.667s-19.115-42.667-42.667-42.667h-768c-23.552 0-42.667 19.115-42.667 42.667s19.115 42.667 42.667 42.667zM128 810.667h768c23.552 0 42.667-19.115 42.667-42.667s-19.115-42.667-42.667-42.667h-768c-23.552 0-42.667 19.115-42.667 42.667s19.115 42.667 42.667 42.667z">
                </path>
              </svg>
            </div>
            <div data-thq="mobile-menu" class="navbar-mobile-menu">
              <div class="navbar-nav">
                <div class="navbar-top">
                  <img alt="Feedback Hub Logo" src="icon.png" class="navbar-logo">
                  <div data-thq="close-menu" class="navbar-close-menu">
                    <svg viewBox="0 0 1024 1024" class="navbar-icon">
                      <path
                        d="M810 274l-238 238 238 238-60 60-238-238-238 238-60-60 238-238-238-238 60-60 238 238 238-238z">
                      </path>
                    </svg>
                  </div>
                </div>
                <nav class="navbar-links1">
                  <a href="index.html" class="link body-small">
                    <span>Home</span>
                  </a>
                  <a href="products.html" class="link body-small">
                    <span>Products</span>
                  </a>
                  <a href="reviews.html" class="link body-small">
                    <span>Reviews</span>
                  </a>
                  <a href="response.html" class="link body-small">
                  <span>Response</span>
                </a>
                <a href="index.html#about-us" class="link body-small">
                    <span>About Us</span>
                  </a>
                  <a href="index.html#contact-contant" class="link body-small">
                    <span>Contact Us</span>
                  </a>
                </nav>
              </div>
              <div class="navbar-buttons">
                <a href="/login.html" class="button-filled">Login</a>
                <a href="/signup.html" class="button-outline">Register</a>
              </div>
            </div>
          </div>
      </header>
  </div>

  <div id="main-content" class="main-contant products-page">
    <!-- Hero Section -->
    <section class="products-hero section-padding">
      <div class="section-max-width">
        <div class="hero-content">
          <h1 class="hero-text heading-1">
            <span>Discover Amazing Products</span>
          </h1>
          <p class="hero-text1 body-large">
            <span>
              Explore our curated collection of products with comprehensive reviews and ratings.
              Find the perfect product for your needs with AI-powered insights.
            </span>
          </p>
        </div>
      </div>
    </section>

    <!-- Search and Filter Section -->
    <section class="search-filter-section section-padding">
      <div class="section-max-width">
        <div class="search-filter-container">
          <div class="search-bar-container">
            <input type="text" id="product-search" placeholder="Search products..." class="search-input">
            <button class="search-btn" id="search-btn">
              <svg viewBox="0 0 24 24" class="search-icon">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </button>
          </div>
          <div class="filter-controls">
            <select id="category-filter" class="filter-select">
              <option value="">All Categories</option>
              <option value="Electronics">Electronics</option>
              <option value="Home & Garden">Home & Garden</option>
              <option value="Fashion">Fashion</option>
              <option value="Sports">Sports</option>
              <option value="Books">Books</option>
              <option value="Health">Health</option>
            </select>
            <select id="price-filter" class="filter-select">
              <option value="">All Prices</option>
              <option value="0-50">$0 - $50</option>
              <option value="50-100">$50 - $100</option>
              <option value="100-200">$100 - $200</option>
              <option value="200-500">$200 - $500</option>
              <option value="500+">$500+</option>
            </select>
            <select id="rating-filter" class="filter-select">
              <option value="">All Ratings</option>
              <option value="4+">4+ Stars</option>
              <option value="3+">3+ Stars</option>
              <option value="2+">2+ Stars</option>
            </select>
            <select id="sort-by" class="filter-select">
              <option value="name">Sort by Name</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
              <option value="newest">Newest First</option>
            </select>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Grid Section -->
    <section class="products-grid-section section-padding">
      <div class="section-max-width">
        <div class="products-stats">
          <span id="products-count">Loading products...</span>
          <button class="view-toggle" id="view-toggle">
            <span class="view-text">Grid View</span>
          </button>
        </div>
        
        <div class="products-grid" id="products-grid">
          <!-- Products will be loaded here dynamically -->
          <div class="loading-products">
            <div class="loading-spinner"></div>
            <p class="body-large">Loading amazing products...</p>
          </div>
        </div>

        <div class="load-more-container">
          <button class="button-outline" id="load-more-products">
            <span class="body-small">Load More Products</span>
          </button>
        </div>
      </div>
    </section>
  </div>

  <footer class="footer2-container" role="contentinfo">
        <div class="footer2-content">
            <p class="copyright-text">© 2024, FeedBack Hub, Inc. Various trademarks held by their respective owners.
                FeedBack, Inc. Feedback Tower, 416 Mission Street, 8rd Floor, San Francisco, United States</p>

            <div class="footer2-links-container">
                <nav class="footer2-nav" aria-label="footer2 navigation">
                    <a href="#" tabindex="0">Legal</a>
                    <a href="#" tabindex="0">Privacy Information</a>
                    <a href="#" tabindex="0">Responsible Disclosure</a>
                    <a href="#" tabindex="0">Terms of Use</a>
                    <a href="#" tabindex="0">Trust</a>
                    <a href="#" tabindex="0">Help</a>
                    <a href="#" tabindex="0">Contact</a>
                    <button type="button" tabindex="0">Cookie Preferences</button>
                </nav>

                <div class="privacy-choices" role="complementary">
                    <img loading="lazy" src="/images/Index/right.png" alt="" class="privacy-icon" />
                    <span class="privacy-text">Your Privacy Choices</span>
                </div>
            </div>
        </div>
    </footer>

  <script data-section-id="navbar" src="https://unpkg.com/@teleporthq/teleport-custom-scripts"></script>
  <script>
    // Check if user is logged in and update navigation
    document.addEventListener('DOMContentLoaded', () => {
      const token = localStorage.getItem('token');
      const guestActions = document.getElementById('guest-actions');
      const userActions = document.getElementById('user-actions');
      const logoutBtn = document.getElementById('logout-btn');

      if (token) {
        // User is logged in
        if (guestActions) guestActions.style.display = 'none';
        if (userActions) userActions.style.display = 'flex';
      } else {
        // User is not logged in
        if (guestActions) guestActions.style.display = 'flex';
        if (userActions) userActions.style.display = 'none';
      }

      // Logout functionality
      if (logoutBtn) {
        logoutBtn.addEventListener('click', (e) => {
          e.preventDefault();
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.reload();
        });
      }
    });
  </script>
  <script src="js/api.js"></script>
  <script src="js/profile-menu.js"></script>
  <script src="js/products-catalog.js"></script>

</body>

</html>
