<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .login-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-users {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
        }
        .test-users h4 {
            margin-top: 0;
        }
        .test-user {
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>Quick Login Test</h2>
        <p>Use this to quickly login and test the profile page functionality.</p>
        
        <form id="login-form">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="password123" required>
            </div>
            <button type="submit" id="login-btn">Login</button>
        </form>
        
        <div id="message"></div>
        
        <div class="test-users">
            <h4>Test Users (if available):</h4>
            <div class="test-user">Email: <EMAIL> | Password: password123</div>
            <div class="test-user">Email: <EMAIL> | Password: password123</div>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="goToProfile()" style="background: #28a745;">Go to Profile Page</button>
            <button onclick="clearStorage()" style="background: #dc3545;">Clear Storage</button>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script>
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');
            const messageDiv = document.getElementById('message');
            
            // Show loading state
            loginBtn.textContent = 'Logging in...';
            loginBtn.disabled = true;
            messageDiv.innerHTML = '';
            
            try {
                console.log('Attempting login with:', email);
                const result = await api.login(email, password);
                console.log('Login result:', result);
                
                if (result.success) {
                    localStorage.setItem('token', result.token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    
                    messageDiv.innerHTML = `
                        <div class="message success">
                            ✅ Login successful!<br>
                            Welcome ${result.user.firstName} ${result.user.lastName}<br>
                            Token saved to localStorage
                        </div>
                    `;
                    
                    console.log('Login successful, token saved');
                    
                    // Auto-redirect after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'profile.html';
                    }, 2000);
                    
                } else {
                    throw new Error(result.error || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                messageDiv.innerHTML = `
                    <div class="message error">
                        ❌ Login failed: ${error.message}
                    </div>
                `;
            } finally {
                loginBtn.textContent = 'Login';
                loginBtn.disabled = false;
            }
        });
        
        function goToProfile() {
            window.location.href = 'profile.html';
        }
        
        function clearStorage() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            document.getElementById('message').innerHTML = `
                <div class="message success">
                    🗑️ Storage cleared
                </div>
            `;
        }
        
        // Check if already logged in
        document.addEventListener('DOMContentLoaded', () => {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            if (token && user) {
                const userData = JSON.parse(user);
                document.getElementById('message').innerHTML = `
                    <div class="message success">
                        ℹ️ Already logged in as ${userData.firstName} ${userData.lastName}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
