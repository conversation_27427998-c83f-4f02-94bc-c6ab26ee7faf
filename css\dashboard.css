/* Dashboard Styles - Profile Layout Integration */

/* Products Grid in Reviews Section */
.reviews-section .products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

/* Reviews List in Badges Section */
.badges-section .reviews-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

/* Quick Action Buttons */
.quick-action-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
  width: 100%;
  margin-bottom: 0.5rem;
}

.quick-action-btn:hover {
  background: #0056b3;
}

/* Enhanced Stat Items for Dashboard */
.track-stats .stat-item {
  text-align: center;
  padding: 0.5rem;
}

.track-stats .stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.track-stats .stat-label {
  font-size: 0.9rem;
  color: #666;
  margin: 0.25rem 0 0;
}

/* Product Cards */
.product-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  border: 1px solid #f0f0f0;
  transition: transform 0.2s ease;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.product-card img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.product-card h3 {
  margin: 1rem;
  font-size: 1.2rem;
  color: #333;
}

.product-card p {
  margin: 0 1rem 1rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.product-card .price {
  font-weight: bold;
  color: #333;
  font-size: 1.1rem;
}

.product-card .btn,
.product-card .review-btn {
  margin: 0 1rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.product-card .btn:hover,
.product-card .review-btn:hover {
  background: #0056b3;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
  margin: 0 1rem 1rem;
}

.product-actions .btn {
  flex: 1;
  text-align: center;
  margin: 0;
}

.btn-outline {
  background: transparent;
  border: 2px solid #007bff;
  color: #007bff;
  text-decoration: none;
}

.btn-outline:hover {
  background: #007bff;
  color: white;
}

/* Review Cards */
.review-card {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid #e9e9e9;
}

.review-card h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 1.1rem;
}

.review-card .rating {
  color: #f8c51c;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.review-card p {
  margin: 0.5rem 0;
  color: #555;
  line-height: 1.4;
}

.review-card small {
  color: #999;
  font-size: 0.8rem;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #fff;
  border-radius: 8px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  position: relative;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
  max-height: 80vh;
  overflow-y: auto;
}

.close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  transition: color 0.2s ease;
}

.close:hover {
  color: #333;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  box-sizing: border-box;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

.btn:hover {
  background: #0056b3;
}

/* Notifications */
.notifications-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  margin-bottom: 0.5rem;
}

.notification-item.unread {
  background: #f0f8ff;
  border-left: 3px solid #007bff;
}

.notification-item.read {
  background: #f9f9f9;
  opacity: 0.7;
}

.notification-item p {
  margin: 0 0 0.5rem;
  color: #333;
}

.notification-item small {
  color: #666;
  font-size: 0.8rem;
}

.mark-read-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.mark-read-btn:hover {
  background: #218838;
}

/* Unified Profile Page Styles */

/* Dashboard Stats Section */
.dashboard-stats-section {
  margin-bottom: 2rem;
}

.dashboard-stats-section h2 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  border: 1px solid #f0f0f0;
  text-align: center;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.stat-card h3 {
  margin: 0 0 0.5rem;
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.stat-card p {
  margin: 0;
  font-size: 2rem;
  font-weight: bold;
  color: #333;
}

/* Products Section */
.products-section {
  margin-bottom: 2rem;
}

.products-section h2 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Reviews Section */
.reviews-section h2 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
}

.reviews-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Quick Actions in Sidebar */
.quick-actions {
  margin-top: 2rem;
}

.quick-actions h2 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
  text-align: left;
}

.action-btn:hover {
  background: #0056b3;
}

.action-btn i {
  width: 16px;
  height: 16px;
}

/* Icon styles for action buttons */
.icon-plus::before { content: "➕"; }
.icon-notifications::before { content: "🔔"; }
.icon-settings::before { content: "⚙️"; }

/* Profile Page Specific Styles */
.user-reviews-display {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
}

.user-reviews-display h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.2rem;
}

.reviews-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.review-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #333;
}

.review-rating {
  color: #f8c51c;
  font-size: 0.9rem;
}

.review-content {
  color: #555;
  line-height: 1.4;
  margin: 0.5rem 0;
}

.review-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #999;
}

.review-source {
  background: #f0f0f0;
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  font-size: 0.75rem;
}

.view-all-reviews-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.view-all-reviews-btn:hover {
  background: #0056b3;
}

.all-reviews-container {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.all-reviews-container .review-card {
  margin-bottom: 1rem;
}

/* Enhanced Add Review Card */
.add-review-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.add-review-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Profile Avatar Hover Effect */
.profile-avatar {
  transition: transform 0.2s ease;
}

.profile-avatar:hover {
  transform: scale(1.02);
}

/* Enhanced Profile Layout */
.profile-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-main section {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #f0f0f0;
  transition: box-shadow 0.3s ease;
}

.profile-main section:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

.profile-main section h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.4rem;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

/* Profile Header Enhancement */
.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
}

.profile-info {
  position: relative;
  z-index: 1;
}

.profile-name {
  color: rgba(45, 45, 45, 0.9) !important;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.profile-role,
.profile-location  {
  color: rgba(45, 45, 45, 0.9) !important;
}

/* Dashboard Stats Grid Enhancement */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 0;
}

.stat-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-card h3 {
  color: #495057;
  font-weight: 600;
}

.stat-card p {
  color: #007bff;
  font-size: 2.2rem;
}

/* Products Grid Enhancement */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.product-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-card img {
  height: 200px;
  transition: transform 0.3s ease;
}

.product-card:hover img {
  transform: scale(1.05);
}

/* Reviews List Enhancement */
.reviews-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.review-card {
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
}

.review-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.review-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #007bff, #0056b3);
}

/* Sidebar Enhancement */
.profile-sidebar {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.profile-sidebar section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid #e9ecef;
}

.profile-sidebar section:last-child {
  margin-bottom: 0;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .profile-container {
    grid-template-columns: 1fr;
  }

  .profile-sidebar {
    order: 0;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .profile-main section {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .reviews-list {
    grid-template-columns: 1fr;
  }

  .review-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .action-btn {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .profile-main section {
    padding: 1rem;
  }

  .profile-info {
    flex-direction: column;
    text-align: center;
  }

  .edit-profile-btn {
    position: static;
    margin-top: 1rem;
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.product-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  border: 1px solid #f0f0f0;
  transition: transform 0.2s ease;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.product-card img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.product-card h3 {
  margin: 1rem;
  font-size: 1.2rem;
  color: #333;
}

.product-card p {
  margin: 0 1rem 1rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.product-card .price {
  font-weight: bold;
  color: #333;
  font-size: 1.1rem;
}

.product-card .btn {
  margin: 0 1rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.product-card .btn:hover {
  background: #0056b3;
}

.reviews-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.review-card {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid #e9e9e9;
}

.review-card h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 1.1rem;
}

.review-card .rating {
  color: #f8c51c;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.review-card p {
  margin: 0.5rem 0;
  color: #555;
  line-height: 1.4;
}

.review-card small {
  color: #999;
  font-size: 0.8rem;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #fff;
  border-radius: 8px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  position: relative;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  transition: color 0.2s ease;
}

.close:hover {
  color: #333;
}

#review-form .form-group {
  margin-bottom: 1rem;
}

#review-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #333;
}

#review-form select,
#review-form textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

#review-form textarea {
  min-height: 100px;
  resize: vertical;
}

#review-form button[type="submit"] {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

#review-form button[type="submit"]:hover {
  background: #0056b3;
}

/* Responsive */
@media (min-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 767px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .dashboard-stats {
    margin-top: 1rem;
    width: 100%;
    justify-content: space-between;
  }
  
  .stat-card {
    flex: 1;
    min-width: 100px;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .reviews-list {
    grid-template-columns: 1fr;
  }
}

/* Loading states */
.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.loading::after {
  content: "...";
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    color: rgba(0,0,0,0);
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  40% {
    color: #666;
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  60% {
    text-shadow:
      .25em 0 0 #666,
      .5em 0 0 rgba(0,0,0,0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 #666,
      .5em 0 0 #666;
  }
}
