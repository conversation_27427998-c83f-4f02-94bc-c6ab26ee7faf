:root {
  --color-primary: #048149;
  --color-secondary: #006dcc;
  --color-text: #1e1e1e;
  --color-text-muted: #585858;
  --color-background: #fff;
  --color-background-alt: #f5f5f5;
  --color-border: #e2e2e2;
  --shadow-sm: 0 2px 0 -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 5px 20px rgba(0, 0, 0, 0.2);
  --font-family: "Segoe UI", system-ui, -apple-system, sans-serif;
  --spacing-base: 8px;
  --radius-base: 8px;
  --transition-base: 0.2s ease-in-out;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  line-height: 1.5;
  color: var(--color-text);
  background: var(--color-background-alt);
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  z-index: 100;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 0;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.logo-link {
  display: block;
}

.logo {
  display: block;
  height: auto;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 24px;
}

.nav-link {
  color: var(--color-text);
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: var(--radius-base);
  transition: background-color var(--transition-base);
}

.nav-link:hover,
.nav-link:focus {
  background-color: var(--color-background-alt);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notifications-btn,
.logout-btn {
  padding: 8px 16px;
  border: 1px;
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: background-color var(--transition-base);
}

.notifications-btn {
  background: transparent;
}

.logout-btn {
  background: var(--color-background);
  border: 1px solid var(--color-secondary);
  color: var(--color-secondary);
  font-weight: 500;
}

.logout-btn:hover,
.logout-btn:focus {
  background: var(--color-secondary);
  color: var(--color-background);
}

.profile-layout {
  margin-top: 85px;
  padding: 32px;
}

.profile-container {
  max-width: 1280px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 32px;
}

.profile-info {
  display: flex;
  align-items: flex-start;
  gap: 32px;
}

.profile-avatar {
  border-radius: var(--radius-base);
  object-fit: cover;
}

.profile-details {
  flex: 1;
}

.profile-name {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
}

.profile-role {
  font-size: 20px;
  color: var(--color-text-muted);
  margin-bottom: 4px;
}

.profile-location {
  font-size: 16px;
  color: var(--color-text-muted);
}

.badges-section,
.reviews-section {
  background: var(--color-background);
  border-radius: var(--radius-base);
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: var(--shadow-sm);
}

.badges-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  list-style: none;
}

.badge-item {
  text-align: center;
}

.badge-item img {
  margin-bottom: 16px;
}

.track-rank,
.connections {
  background: var(--color-background);
  border-radius: var(--radius-base);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-sm);
}

.connection-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  text-align: center;
  margin-top: 24px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-primary);
}

.stat-label {
  font-size: 14px;
  color: var(--color-text-muted);
}

@media (max-width: 1151px) {
  .profile-container {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .nav-list {
    flex-wrap: wrap;
  }
}

@media (max-width: 952px) {
  .profile-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .badges-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .badges-grid {
    grid-template-columns: 1fr;
  }

  .connection-stats {
    grid-template-columns: 1fr;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

.footer2-container {
  justify-content: center;
  align-items: center;
  align-self: stretch;
  background: var(--www-salesforce-com-white, #fff);
  background-color: var(--www-salesforce-com-white, #fff);
  display: flex;
  flex-direction: column;
  color: var(--www-salesforce-com-cod-gray, var(--color-grey-9, #181818));
  padding: 24px 80px;
  font: var(--font-weight-400, 400) var(--font-size-12, 12px) / var(--line-height-18, 18px) var(--font-family-Font-1, "Segoe UI");
}

.footer2-content {
  display: flex;
  width: 907px;
  max-width: 100%;
  flex-direction: column;
}

.copyright-text {
  letter-spacing: var(--letter-spacing-0_02, 0.024px);
}

.footer2-links-container {
  align-self: end;
  display: flex;
  margin-top: 16px;
  gap: 16px;
  flex-wrap: wrap;
}

.footer2-nav {
  align-self: start;
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  flex-grow: 1;
  flex-basis: auto;
}

.privacy-choices {
  display: flex;
  gap: 4px;
}

.privacy-icon {
  aspect-ratio: 2.13;
  object-fit: contain;
  object-position: center;
  width: 32px;
  margin: auto 0;
}

.privacy-text {
  padding-bottom: 1px;
}

@media (max-width: 991px) {
  .footer2-container {
    padding: 0 20px;
  }

  .copyright-text {
    max-width: 100%;
  }

  .footer2-links-container {
    margin-right: 10px;
  }
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.edit-profile-btn {
  background: transparent;
  color: var(--color-background);
  border-radius: var(--radius-base);
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-base);
}

.edit-profile-btn:hover {
  background: #d3d3d3
}