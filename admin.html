<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - FeedbackHub</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="index.css">
    <link rel="stylesheet" href="profile.css">
    <link rel="stylesheet" href="css/profile-menu.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="icon" href="ico.ico" type="image/x-icon" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header class="main-header" role="banner">
        <div class="header-content">
            <a href="/index.html" class="logo-link">
                <img src="icon_trans.png" alt="Feedback Hub" width="184" height="80" class="logo">
            </a>
            <div data-thq="navbar-nav" class="navbar-desktop-menu">
                <nav class="navbar-links" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li><a href="products.html" class="nav-link">Products</a></li>
                        <li><a href="reviews.html" class="nav-link">Reviews</a></li>
                        <li><a href="index.html#community-content" class="nav-link">Industry</a></li>
                        <li><a href="response.html" class="nav-link">Response</a></li>
                        <li><a href="index.html#contact-contant" class="nav-link">Support</a></li>
                        <li><a href="index.html#about-us" class="nav-link">About Us</a></li>
                    </ul>
                </nav>
                <div class="user-actions" role="navigation" aria-label="User menu" id="guest-actions" style="display: none;">
                    <a href="/login.html" class="button-outline">
                        <span class="body-small">Log in</span>
                    </a>
                    <a href="/signup.html" class="button-filled">
                        <span class="body-small">Sign up</span>
                    </a>
                </div>
                <div class="user-actions" role="navigation" aria-label="User menu" id="user-actions">
                    <div class="profile-menu-container">
                        <button class="profile-menu-trigger" id="profile-menu-trigger" aria-expanded="false" aria-haspopup="true">
                            <img src="profile photo.svg" alt="Profile" class="profile-icon" id="header-profile-icon">
                            <i class="fas fa-chevron-down profile-arrow"></i>
                        </button>
                        <div class="profile-dropdown" id="profile-dropdown" role="menu">
                            <div class="profile-dropdown-header">
                                <img src="profile photo.svg" alt="Profile" class="dropdown-profile-icon" id="dropdown-profile-icon">
                                <div class="dropdown-user-info">
                                    <span class="dropdown-user-name" id="dropdown-user-name">Loading...</span>
                                    <span class="dropdown-user-email" id="dropdown-user-email">Loading...</span>
                                </div>
                            </div>
                            <div class="profile-dropdown-divider"></div>
                            <a href="profile.html" class="profile-dropdown-item" role="menuitem">
                                <i class="fas fa-user"></i>
                                <span>Profile</span>
                            </a>
                            <a href="admin.html" class="profile-dropdown-item admin-only" role="menuitem">
                                <i class="fas fa-cog"></i>
                                <span>Admin Panel</span>
                            </a>
                            <div class="profile-dropdown-divider"></div>
                            <button class="profile-dropdown-item logout-item" id="logout-btn" role="menuitem">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main id="main-content" class="admin-layout" role="main">
        <div class="admin-container">
            <!-- Sidebar Navigation -->
            <aside class="admin-sidebar">
                <div class="admin-nav">
                    <h2 class="admin-nav-title">
                        <i class="fas fa-shield-alt"></i>
                        Admin Panel
                    </h2>
                    <nav class="admin-nav-menu">
                        <button class="admin-nav-item active" data-section="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </button>
                        <button class="admin-nav-item" data-section="users">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </button>
                        <button class="admin-nav-item" data-section="products">
                            <i class="fas fa-box"></i>
                            <span>Products</span>
                        </button>
                        <button class="admin-nav-item" data-section="reviews">
                            <i class="fas fa-star"></i>
                            <span>Reviews</span>
                        </button>
                        <button class="admin-nav-item" data-section="analytics">
                            <i class="fas fa-chart-bar"></i>
                            <span>Analytics</span>
                        </button>
                        <button class="admin-nav-item" data-section="bulk-operations">
                            <i class="fas fa-tasks"></i>
                            <span>Bulk Operations</span>
                        </button>
                        <button class="admin-nav-item" data-section="system">
                            <i class="fas fa-cogs"></i>
                            <span>System</span>
                        </button>
                        <button class="admin-nav-item" data-section="logs">
                            <i class="fas fa-file-alt"></i>
                            <span>Activity Logs</span>
                        </button>
                    </nav>
                </div>
            </aside>

            <!-- Main Content Area -->
            <div class="admin-main">
                <!-- Dashboard Section -->
                <section id="dashboard-section" class="admin-section active">
                    <div class="admin-header">
                        <h1>Dashboard Overview</h1>
                        <p>Welcome to the FeedbackHub Admin Panel</p>
                    </div>

                    <!-- Stats Cards -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="total-users">0</h3>
                                <p>Total Users</p>
                                <span class="stat-change" id="active-users">0 active</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="total-products">0</h3>
                                <p>Total Products</p>
                                <span class="stat-change" id="new-products">0 this week</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="total-reviews">0</h3>
                                <p>Total Reviews</p>
                                <span class="stat-change" id="new-reviews">0 this week</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="avg-rating">0.0</h3>
                                <p>Average Rating</p>
                                <span class="stat-change" id="new-users">0 new users</span>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="admin-grid">
                        <div class="admin-card">
                            <div class="card-header">
                                <h3>Recent Users</h3>
                                <button class="btn-secondary" onclick="showSection('users')">View All</button>
                            </div>
                            <div class="card-content">
                                <div id="recent-users-list" class="activity-list">
                                    <div class="loading">Loading recent users...</div>
                                </div>
                            </div>
                        </div>
                        <div class="admin-card">
                            <div class="card-header">
                                <h3>Recent Reviews</h3>
                                <button class="btn-secondary" onclick="showSection('reviews')">View All</button>
                            </div>
                            <div class="card-content">
                                <div id="recent-reviews-list" class="activity-list">
                                    <div class="loading">Loading recent reviews...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Users Section -->
                <section id="users-section" class="admin-section">
                    <div class="admin-header">
                        <h1>User Management</h1>
                        <p>Manage user accounts and permissions</p>
                    </div>

                    <!-- Users Controls -->
                    <div class="admin-controls">
                        <div class="search-filters">
                            <input type="text" id="users-search" placeholder="Search users..." class="search-input">
                            <select id="users-role-filter" class="filter-select">
                                <option value="">All Roles</option>
                                <option value="user">User</option>
                                <option value="moderator">Moderator</option>
                                <option value="admin">Admin</option>
                            </select>
                            <select id="users-status-filter" class="filter-select">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                            <button id="users-search-btn" class="btn-primary">
                                <i class="fas fa-search"></i>
                                Search
                            </button>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="admin-card">
                        <div class="card-content">
                            <div class="table-container">
                                <table class="admin-table" id="users-table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Joined</th>
                                            <th>Last Login</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="users-table-body">
                                        <tr>
                                            <td colspan="7" class="loading">Loading users...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination" id="users-pagination"></div>
                        </div>
                    </div>
                </section>

                <!-- Products Section -->
                <section id="products-section" class="admin-section">
                    <div class="admin-header">
                        <h1>Product Management</h1>
                        <p>Manage products and categories</p>
                    </div>

                    <!-- Products Controls -->
                    <div class="admin-controls">
                        <div class="search-filters">
                            <input type="text" id="products-search" placeholder="Search products..." class="search-input">
                            <select id="products-category-filter" class="filter-select">
                                <option value="">All Categories</option>
                            </select>
                            <button id="products-search-btn" class="btn-primary">
                                <i class="fas fa-search"></i>
                                Search
                            </button>
                        </div>
                        <button id="add-product-btn" class="btn-success">
                            <i class="fas fa-plus"></i>
                            Add Product
                        </button>
                    </div>

                    <!-- Products Table -->
                    <div class="admin-card">
                        <div class="card-content">
                            <div class="table-container">
                                <table class="admin-table" id="products-table">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Category</th>
                                            <th>Price</th>
                                            <th>Reviews</th>
                                            <th>Rating</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="products-table-body">
                                        <tr>
                                            <td colspan="7" class="loading">Loading products...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination" id="products-pagination"></div>
                        </div>
                    </div>
                </section>

                <!-- Reviews Section -->
                <section id="reviews-section" class="admin-section">
                    <div class="admin-header">
                        <h1>Review Management</h1>
                        <p>Manage and moderate user reviews</p>
                    </div>

                    <!-- Reviews Controls -->
                    <div class="admin-controls">
                        <div class="search-filters">
                            <input type="text" id="reviews-search" placeholder="Search reviews..." class="search-input">
                            <select id="reviews-rating-filter" class="filter-select">
                                <option value="">All Ratings</option>
                                <option value="5">5 Stars</option>
                                <option value="4">4 Stars</option>
                                <option value="3">3 Stars</option>
                                <option value="2">2 Stars</option>
                                <option value="1">1 Star</option>
                            </select>
                            <select id="reviews-verified-filter" class="filter-select">
                                <option value="">All Reviews</option>
                                <option value="true">Verified</option>
                                <option value="false">Unverified</option>
                            </select>
                            <button id="reviews-search-btn" class="btn-primary">
                                <i class="fas fa-search"></i>
                                Search
                            </button>
                        </div>
                    </div>

                    <!-- Reviews Table -->
                    <div class="admin-card">
                        <div class="card-content">
                            <div class="table-container">
                                <table class="admin-table" id="reviews-table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Product</th>
                                            <th>Rating</th>
                                            <th>Review</th>
                                            <th>Verified</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="reviews-table-body">
                                        <tr>
                                            <td colspan="7" class="loading">Loading reviews...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination" id="reviews-pagination"></div>
                        </div>
                    </div>
                </section>

                <!-- Analytics Section -->
                <section id="analytics-section" class="admin-section">
                    <div class="admin-header">
                        <h1>Analytics & Reports</h1>
                        <p>View system analytics and trends</p>
                    </div>

                    <!-- Analytics Controls -->
                    <div class="admin-controls">
                        <div class="search-filters">
                            <select id="analytics-period" class="filter-select">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                            </select>
                            <button id="refresh-analytics-btn" class="btn-primary">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Analytics Charts -->
                    <div class="admin-grid">
                        <div class="admin-card">
                            <div class="card-header">
                                <h3>User Registration Trend</h3>
                            </div>
                            <div class="card-content">
                                <canvas id="user-trend-chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                        <div class="admin-card">
                            <div class="card-header">
                                <h3>Review Activity</h3>
                            </div>
                            <div class="card-content">
                                <canvas id="review-trend-chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                        <div class="admin-card">
                            <div class="card-header">
                                <h3>Top Categories</h3>
                            </div>
                            <div class="card-content">
                                <div id="top-categories-list" class="category-list">
                                    <div class="loading">Loading categories...</div>
                                </div>
                            </div>
                        </div>
                        <div class="admin-card">
                            <div class="card-header">
                                <h3>Rating Distribution</h3>
                            </div>
                            <div class="card-content">
                                <canvas id="rating-distribution-chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Bulk Operations Section -->
                <section id="bulk-operations-section" class="admin-section">
                    <div class="admin-header">
                        <h1>Bulk Operations</h1>
                        <p>Perform bulk actions on multiple items</p>
                    </div>

                    <!-- Bulk User Operations -->
                    <div class="admin-card">
                        <div class="card-header">
                            <h3>Bulk User Operations</h3>
                        </div>
                        <div class="card-content">
                            <div class="bulk-operation-form">
                                <div class="form-group">
                                    <label for="bulk-user-ids">User IDs (comma-separated):</label>
                                    <textarea id="bulk-user-ids" class="form-control" rows="3" placeholder="Enter user IDs separated by commas"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="bulk-user-action">Action:</label>
                                    <select id="bulk-user-action" class="form-control">
                                        <option value="activate">Activate Users</option>
                                        <option value="deactivate">Deactivate Users</option>
                                        <option value="updateRole">Update Role</option>
                                        <option value="delete">Delete Users</option>
                                    </select>
                                </div>
                                <div class="form-group" id="bulk-user-role-group" style="display: none;">
                                    <label for="bulk-user-role">New Role:</label>
                                    <select id="bulk-user-role" class="form-control">
                                        <option value="user">User</option>
                                        <option value="moderator">Moderator</option>
                                        <option value="admin">Admin</option>
                                    </select>
                                </div>
                                <button type="button" class="btn-primary" onclick="executeBulkUserOperation()">
                                    <i class="fas fa-play"></i>
                                    Execute Operation
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Product Operations -->
                    <div class="admin-card">
                        <div class="card-header">
                            <h3>Bulk Product Operations</h3>
                        </div>
                        <div class="card-content">
                            <div class="bulk-operation-form">
                                <div class="form-group">
                                    <label for="bulk-product-ids">Product IDs (comma-separated):</label>
                                    <textarea id="bulk-product-ids" class="form-control" rows="3" placeholder="Enter product IDs separated by commas"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="bulk-product-action">Action:</label>
                                    <select id="bulk-product-action" class="form-control">
                                        <option value="updateCategory">Update Category</option>
                                        <option value="updatePrice">Update Price (Multiply)</option>
                                        <option value="delete">Delete Products</option>
                                    </select>
                                </div>
                                <div class="form-group" id="bulk-product-category-group">
                                    <label for="bulk-product-category">New Category:</label>
                                    <input type="text" id="bulk-product-category" class="form-control" placeholder="Enter new category">
                                </div>
                                <div class="form-group" id="bulk-product-multiplier-group" style="display: none;">
                                    <label for="bulk-product-multiplier">Price Multiplier:</label>
                                    <input type="number" id="bulk-product-multiplier" class="form-control" step="0.01" value="1.0" placeholder="e.g., 1.1 for 10% increase">
                                </div>
                                <button type="button" class="btn-primary" onclick="executeBulkProductOperation()">
                                    <i class="fas fa-play"></i>
                                    Execute Operation
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Review Operations -->
                    <div class="admin-card">
                        <div class="card-header">
                            <h3>Bulk Review Operations</h3>
                        </div>
                        <div class="card-content">
                            <div class="bulk-operation-form">
                                <div class="form-group">
                                    <label for="bulk-review-ids">Review IDs (comma-separated):</label>
                                    <textarea id="bulk-review-ids" class="form-control" rows="3" placeholder="Enter review IDs separated by commas"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="bulk-review-action">Action:</label>
                                    <select id="bulk-review-action" class="form-control">
                                        <option value="verify">Verify Reviews</option>
                                        <option value="unverify">Unverify Reviews</option>
                                        <option value="delete">Delete Reviews</option>
                                    </select>
                                </div>
                                <button type="button" class="btn-primary" onclick="executeBulkReviewOperation()">
                                    <i class="fas fa-play"></i>
                                    Execute Operation
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- System Section -->
                <section id="system-section" class="admin-section">
                    <div class="admin-header">
                        <h1>System Management</h1>
                        <p>System settings, health monitoring, and data export</p>
                    </div>

                    <!-- System Health -->
                    <div class="admin-card">
                        <div class="card-header">
                            <h3>System Health</h3>
                            <button class="btn-secondary" onclick="refreshSystemHealth()">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                        </div>
                        <div class="card-content">
                            <div id="system-health-content">
                                <div class="loading">Loading system health...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Export -->
                    <div class="admin-card">
                        <div class="card-header">
                            <h3>Data Export</h3>
                        </div>
                        <div class="card-content">
                            <div class="export-options">
                                <div class="export-item">
                                    <h4>Export Users</h4>
                                    <p>Download all user data</p>
                                    <div class="export-buttons">
                                        <button class="btn-primary" onclick="exportData('users', 'json')">
                                            <i class="fas fa-download"></i>
                                            JSON
                                        </button>
                                        <button class="btn-secondary" onclick="exportData('users', 'csv')">
                                            <i class="fas fa-file-csv"></i>
                                            CSV
                                        </button>
                                    </div>
                                </div>
                                <div class="export-item">
                                    <h4>Export Products</h4>
                                    <p>Download all product data</p>
                                    <div class="export-buttons">
                                        <button class="btn-primary" onclick="exportData('products', 'json')">
                                            <i class="fas fa-download"></i>
                                            JSON
                                        </button>
                                        <button class="btn-secondary" onclick="exportData('products', 'csv')">
                                            <i class="fas fa-file-csv"></i>
                                            CSV
                                        </button>
                                    </div>
                                </div>
                                <div class="export-item">
                                    <h4>Export Reviews</h4>
                                    <p>Download all review data</p>
                                    <div class="export-buttons">
                                        <button class="btn-primary" onclick="exportData('reviews', 'json')">
                                            <i class="fas fa-download"></i>
                                            JSON
                                        </button>
                                        <button class="btn-secondary" onclick="exportData('reviews', 'csv')">
                                            <i class="fas fa-file-csv"></i>
                                            CSV
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings -->
                    <div class="admin-card">
                        <div class="card-header">
                            <h3>System Settings</h3>
                        </div>
                        <div class="card-content">
                            <div id="system-settings-content">
                                <div class="loading">Loading system settings...</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Activity Logs Section -->
                <section id="logs-section" class="admin-section">
                    <div class="admin-header">
                        <h1>Activity Logs</h1>
                        <p>Monitor system activity and user actions</p>
                    </div>

                    <!-- Log Controls -->
                    <div class="admin-controls">
                        <div class="search-filters">
                            <select id="logs-type-filter" class="filter-select">
                                <option value="all">All Types</option>
                                <option value="user_login">User Login</option>
                                <option value="admin_action">Admin Action</option>
                                <option value="system_error">System Error</option>
                            </select>
                            <button id="logs-refresh-btn" class="btn-primary">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Activity Logs Table -->
                    <div class="admin-card">
                        <div class="card-content">
                            <div class="table-container">
                                <table class="admin-table" id="logs-table">
                                    <thead>
                                        <tr>
                                            <th>Timestamp</th>
                                            <th>Type</th>
                                            <th>Message</th>
                                            <th>User ID</th>
                                            <th>IP Address</th>
                                        </tr>
                                    </thead>
                                    <tbody id="logs-table-body">
                                        <tr>
                                            <td colspan="5" class="loading">Loading activity logs...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination" id="logs-pagination"></div>
                        </div>
                    </div>
                </section>
            </div>
        </div>

        <!-- Modals -->
        <!-- Edit User Modal -->
        <div id="edit-user-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit User</h3>
                    <button class="modal-close" onclick="closeModal('edit-user-modal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="edit-user-form">
                        <input type="hidden" id="edit-user-id">
                        <div class="form-group">
                            <label for="edit-user-role">Role:</label>
                            <select id="edit-user-role" class="form-control">
                                <option value="user">User</option>
                                <option value="moderator">Moderator</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-user-status">Status:</label>
                            <select id="edit-user-status" class="form-control">
                                <option value="true">Active</option>
                                <option value="false">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="closeModal('edit-user-modal')">Cancel</button>
                    <button type="button" class="btn-primary" onclick="saveUser()">Save Changes</button>
                </div>
            </div>
        </div>

        <!-- Edit Product Modal -->
        <div id="edit-product-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Product</h3>
                    <button class="modal-close" onclick="closeModal('edit-product-modal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="edit-product-form">
                        <input type="hidden" id="edit-product-id">
                        <div class="form-group">
                            <label for="edit-product-name">Name:</label>
                            <input type="text" id="edit-product-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-product-description">Description:</label>
                            <textarea id="edit-product-description" class="form-control" rows="3" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="edit-product-price">Price:</label>
                            <input type="number" id="edit-product-price" class="form-control" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-product-category">Category:</label>
                            <input type="text" id="edit-product-category" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-product-image">Image URL:</label>
                            <input type="url" id="edit-product-image" class="form-control">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="closeModal('edit-product-modal')">Cancel</button>
                    <button type="button" class="btn-primary" onclick="saveProduct()">Save Changes</button>
                </div>
            </div>
        </div>

        <!-- Confirmation Modal -->
        <div id="confirm-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="confirm-title">Confirm Action</h3>
                    <button class="modal-close" onclick="closeModal('confirm-modal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p id="confirm-message">Are you sure you want to perform this action?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="closeModal('confirm-modal')">Cancel</button>
                    <button type="button" class="btn-danger" id="confirm-action-btn">Confirm</button>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/profile-menu.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Global variables for authentication state
        let isAuthenticated = false;
        let userRole = null;
        let authToken = null;

        // Check admin access on page load
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 Admin panel loading...');

            // Show loading state
            document.body.style.opacity = '0.7';

            try {
                await checkAuthentication();
            } catch (error) {
                console.error('❌ Authentication failed:', error);
                showLoginPrompt();
            } finally {
                document.body.style.opacity = '1';
            }
        });

        async function checkAuthentication() {
            const token = localStorage.getItem('token');
            console.log('🔑 Checking token:', token ? 'Found' : 'Not found');

            // Validate token exists and format
            if (!token || token === 'null' || token === 'undefined' || !token.includes('.')) {
                console.log('❌ Invalid or missing token');
                localStorage.removeItem('token');
                showLoginPrompt();
                return;
            }

            try {
                console.log('🔍 Verifying token...');

                // Verify token with backend
                const userResponse = await fetch('/api/auth/me', {
                    headers: {
                        'x-auth-token': token
                    }
                });

                console.log('📡 Auth response status:', userResponse.status);

                if (!userResponse.ok) {
                    console.log('❌ Token validation failed');
                    localStorage.removeItem('token');
                    showLoginPrompt();
                    return;
                }

                const userData = await userResponse.json();
                console.log('✅ User authenticated:', userData.user.email, 'Role:', userData.user.role);

                // Store auth state
                isAuthenticated = true;
                userRole = userData.user.role;
                authToken = token;

                // Check admin/moderator role
                if (userData.user.role !== 'admin' && userData.user.role !== 'moderator') {
                    console.log('❌ Insufficient privileges');
                    alert('Access denied. Admin or moderator privileges required.');
                    window.location.href = 'profile.html';
                    return;
                }

                console.log('🔍 Testing admin access...');

                // Test admin endpoint access
                const adminResponse = await fetch('/api/admin/stats', {
                    headers: {
                        'x-auth-token': token
                    }
                });

                console.log('📡 Admin test status:', adminResponse.status);

                if (!adminResponse.ok) {
                    console.error('❌ Admin access failed:', adminResponse.status);

                    if (adminResponse.status === 403) {
                        alert('Access denied. Admin privileges required.');
                        window.location.href = 'profile.html';
                    } else if (adminResponse.status === 401) {
                        localStorage.removeItem('token');
                        showLoginPrompt();
                    } else {
                        alert('Failed to load admin panel. Please try again.');
                        window.location.href = 'profile.html';
                    }
                    return;
                }

                console.log('✅ Admin access verified!');

                // Initialize admin panel with delay to ensure DOM is ready
                setTimeout(() => {
                    console.log('🎯 Initializing admin panel...');
                    if (typeof initializeAdminPanel === 'function') {
                        initializeAdminPanel();
                        console.log('✅ Admin panel ready!');
                    } else {
                        console.error('❌ Admin panel function not found');
                        alert('Admin panel failed to initialize. Please refresh.');
                    }
                }, 200);

            } catch (error) {
                console.error('❌ Authentication error:', error);
                localStorage.removeItem('token');
                showLoginPrompt();
            }
        }

        function showLoginPrompt() {
            alert('Please log in to access the admin panel.');
            window.location.href = 'login.html';
        }
    </script>
</body>

</html>
